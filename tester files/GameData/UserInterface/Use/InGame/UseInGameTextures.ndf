// !!!!!! ATTENTION
// Toutes les textures listées ici doivent forcement se trouver dans GameData:/Assets/2D/Interface
// !!!!!! ATTENTION

//Ex : InGameTexture_Blablabla is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/InGame/Blablabla.png")

UseInGame_AdditionalTextureBank is TBUCKToolAdditionalTextureBank
(
    Textures = MAP
    [
        ("UseInGame_UnitInfoPanel_TurnPageRight",             MAP [(~/ComponentState/Normal, ~/UseInGame_UnitInfoPanel_TurnPageRight),
                                                                   (~/ComponentState/Clicked, ~/UseInGame_UnitInfoPanel_TurnPageRight),
                                                                   (~/ComponentState/Highlighted, ~/UseInGame_UnitInfoPanel_TurnPageRight),
                                                                   (~/ComponentState/Toggled, ~/UseInGame_UnitInfoPanel_TurnPageRight)]),
        ("UseInGame_UnitInfoPanel_TurnPageLeft",              MAP [(~/ComponentState/Normal, ~/UseInGame_UnitInfoPanel_TurnPageLeft),
                                                                   (~/ComponentState/Clicked, ~/UseInGame_UnitInfoPanel_TurnPageLeft),
                                                                   (~/ComponentState/Highlighted, ~/UseInGame_UnitInfoPanel_TurnPageLeft),
                                                                   (~/ComponentState/Toggled, ~/UseInGame_UnitInfoPanel_TurnPageLeft)]),
        ("UseInGame_UnitInfoPanel_DefaultVehicle",            MAP [(~/ComponentState/Normal, ~/UseInGame_UnitInfoPanel_DefaultVehicle)] ),
        ("UseInGame_UnitInfoPanel_DefaultHelico",             MAP [(~/ComponentState/Normal, ~/UseInGame_UnitInfoPanel_DefaultHelico)] ),
        ("UseInGame_UnitInfoPanel_DefaultInfantry",          MAP [(~/ComponentState/Normal, ~/UseInGame_UnitInfoPanel_DefaultInfantry)] ),

        ("UseInGame_Transport_ATGM",                          MAP [(~/ComponentState/Normal, ~/UseInGame_Transport_ATGM)] ),
        ("UseInGame_Transport_LAAD",                          MAP [(~/ComponentState/Normal, ~/UseInGame_Transport_LAAD)] ),
        ("UseInGame_Transport_COMMAND",                       MAP [(~/ComponentState/Normal, ~/UseInGame_Transport_COMMAND)] ),
        ("UseInGame_Transport_RECO",                          MAP [(~/ComponentState/Normal, ~/UseInGame_Transport_RECO)] ),
        ("UseInGame_Transport_SUPPORT",                       MAP [(~/ComponentState/Normal, ~/UseInGame_Transport_SUPPORT)] ),
        ("UseInGame_Transport_FLAME",                         MAP [(~/ComponentState/Normal, ~/UseInGame_Transport_FLAME)] ),
        ("UseInGame_Transport_REGINF",                        MAP [(~/ComponentState/Normal, ~/UseInGame_Transport_REGINF)] ),

        ("UseInGame_Transport_assault",                        MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/RTS/assault.png'))] ),

        ("UseInGame_Transport_HMG", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseInGame/LabelIcons/RTS/HMG.png"))] ),

        ("UseInGame_Transport_ELIINF",                        MAP [(~/ComponentState/Normal, ~/UseInGame_Transport_ELIINF)] ),
        ("UseInGame_Transport_APC",                           MAP [(~/ComponentState/Normal, ~/UseInGame_Transport_APC)] ),
        ("UseInGame_Transport_ARMORED",                       MAP [(~/ComponentState/Normal, ~/UseInGame_Transport_ARMORED)] ),
        ("UseInGame_Transport_CMD",                           MAP [(~/ComponentState/Normal, ~/UseInGame_Transport_CMD)] ),
        ("UseInGame_Transport_howitzer",                      MAP [(~/ComponentState/Normal, ~/UseInGame_Transport_howitzer)] ),
        ("UseInGame_Transport_flak",                          MAP [(~/ComponentState/Normal, ~/UseInGame_Transport_flak)] ),
        ("UseInGame_Transport_supply",                        MAP [(~/ComponentState/Normal, ~/UseInGame_Transport_supply)] ),
        ("UseInGame_Transport_ATGun",                         MAP [(~/ComponentState/Normal, ~/UseInGame_Transport_ATGun)] ),
        ("UseInGame_Transport_Mortar",                        MAP [(~/ComponentState/Normal, ~/UseInGame_Transport_Mortar)] ),
        ("UseInGame_Transport_HOWZ",                          MAP [(~/ComponentState/Normal, ~/UseInGame_Transport_HOWZ)] ),
        ("UseInGame_Transport_UNKNOW",                        MAP [(~/ComponentState/Normal, ~/UseInGame_Transport_UNKNOW)] ),

        ("UseInGame_Transport_Tank",                        MAP [(~/ComponentState/Normal,  TUIResourceTexture_Common( FileName = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/RTS/Armor.png'))] ),
        ("UseInGame_Transport_Plane",                        MAP [(~/ComponentState/Normal,  TUIResourceTexture_Common( FileName = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/RTS/AA_air.png'))] ),
        ("UseInGame_Transport_Helo",                        MAP [(~/ComponentState/Normal,  TUIResourceTexture_Common( FileName = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/RTS/hel.png'))] ),
        ("UseInGame_Transport_Support",                        MAP [(~/ComponentState/Normal,  TUIResourceTexture_Common( FileName = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/RTS/appui.png'))] ),


//       Curseurs tactiques
        ("Texture_Tactical_Cursor_Forbidden",                 MAP [(~/ComponentState/Normal, ~/Texture_Tactical_Cursor_Forbidden)] ),
        ("Texture_Tactical_Cursor_UnguidedMissile",           MAP [(~/ComponentState/Normal, ~/Texture_Tactical_Cursor_UnguidedMissile)] ),
        ("Texture_Tactical_Cursor_Inefficient",               MAP [(~/ComponentState/Normal, ~/Texture_Tactical_Cursor_Inefficient)] ),
        ("Texture_Tactical_Cursor_AirMissile",                MAP [(~/ComponentState/Normal, ~/Texture_Tactical_Cursor_AirMissile)] ),
        ("Texture_Tactical_Cursor_Artillery",                 MAP [(~/ComponentState/Normal, ~/Texture_Tactical_Cursor_Artillery)] ),
        ("Texture_Tactical_Cursor_Canon",                     MAP [(~/ComponentState/Normal, ~/Texture_Tactical_Cursor_Canon)] ),
        ("Texture_Tactical_Cursor_MoveAndExplode",            MAP [(~/ComponentState/Normal, ~/Texture_Tactical_Cursor_MoveAndExplode)] ),
        ("Texture_Tactical_Cursor_FastCanon",                 MAP [(~/ComponentState/Normal, ~/Texture_Tactical_Cursor_FastCanon)] ),
        ("Texture_Tactical_Cursor_GroundMissile",             MAP [(~/ComponentState/Normal, ~/Texture_Tactical_Cursor_GroundMissile)] ),
        ("Texture_Tactical_Cursor_MG",                        MAP [(~/ComponentState/Normal, ~/Texture_Tactical_Cursor_MG)] ),
        ("Texture_Tactical_Cursor_SuppressFire",              MAP [(~/ComponentState/Normal, ~/Texture_Tactical_Cursor_SuppressFire)] ),
        ("Texture_Tactical_Cursor_SelectionPointer",          MAP [(~/ComponentState/Normal, ~/Texture_Tactical_Cursor_SelectionPointer)] ),
        ("Texture_Tactical_Cursor_SquarePointer",             MAP [(~/ComponentState/Normal, ~/Texture_Tactical_Cursor_SquarePointer)] ),

//------icones de vitesse de jeu
        ("vitesse01", MAP [
                            (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/GameSpeed/speed_pause.png' )),
                            (~/ComponentState/Clicked, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/GameSpeed/speed_pause_normal.png' )),
                            (~/ComponentState/Highlighted, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/GameSpeed/speed_pause_normal.png' )),
                            (~/ComponentState/Toggled, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/GameSpeed/speed_pause_normal.png' )),
                            ] ),
        ("vitesse02", MAP [
                            (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/GameSpeed/speed_01.png' )),
                            (~/ComponentState/Clicked, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/GameSpeed/speed_01_normal.png' )),
                            (~/ComponentState/Highlighted, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/GameSpeed/speed_01_normal.png' )),
                            (~/ComponentState/Toggled, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/GameSpeed/speed_01_normal.png' )),
                            ] ),

        ("vitesse03", MAP [
                            (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/GameSpeed/speed_02.png' )),
                            (~/ComponentState/Clicked, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/GameSpeed/speed_02_normal.png' )),
                            (~/ComponentState/Highlighted, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/GameSpeed/speed_02_normal.png' )),
                            (~/ComponentState/Toggled, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/GameSpeed/speed_02_normal.png' )),
                            ] ),
        ("vitesse04", MAP [
                            (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/GameSpeed/speed_03.png' )),
                            (~/ComponentState/Clicked, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/GameSpeed/speed_03_normal.png' )),
                            (~/ComponentState/Highlighted, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/GameSpeed/speed_03_normal.png' )),
                            (~/ComponentState/Toggled, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/GameSpeed/speed_03_normal.png' )),
                            ] ),
        ("vitesse05", MAP [
                            (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/GameSpeed/speed_04.png' )),
                            (~/ComponentState/Clicked, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/GameSpeed/speed_04_normal.png' )),
                            (~/ComponentState/Highlighted, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/GameSpeed/speed_04_normal.png' )),
                            (~/ComponentState/Toggled, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/GameSpeed/speed_04_normal.png' )),
                            ] ),

//-------------------------------------------------------------------------------------// icones de flare
        ("textureFlareAttack", MAP [
                            (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/FlarePanel/attack.png' )),
                            ] ),
        ("textureFlareDefense", MAP [
                            (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/FlarePanel/defense.png' )),

                            ] ),

        ("textureFlareCustom", MAP [
                            (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/FlarePanel/custom.png' )),

                            ] ),
        ("textureFlareHelp", MAP [
                            (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/FlarePanel/help.png' )),
                            ] ),
        ("textureFlareFireSupport", MAP [
                            (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/FlarePanel/fireSupport.png' )),

                            ] ),
        ("textureFlareEnemySpotted", MAP [
                            (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/FlarePanel/enemySpotted.png' )),
                            ] ),

//-------------------------------------------------------------------------------------// icones de player mission
        ("texturePlayerMissionSeize", MAP [
                            (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/LabelIcons/Seize_Beacon.png' )),
                            ] ),

//-------------------------------------------------------------------------------------// Score panel icons
        ("texturePlayerMuted", MAP [
                            (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/Icones/unmuted.png' )),
                            (~/ComponentState/Toggled, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/Icones/muted.png' )),
                            ] ),
        ("icone_scoreVictoire",      MAP [ (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/Icones/scoreVictoire.png' )), ] ),
        ("icone_scorePoints",      MAP [ (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/Icones/scorePoints.png' )), ] ),
        ("icone_moralePoints",      MAP [ (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/Icones/moralePoints.png' )), ] ),
//-------------------------------------------------------------------------------------// icones pour chacher le panneau de regles d'engagement
        // ROE
        ("EngagementRulesHide", MAP [
                            (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/EngagementRules/close.png' )),
                            //(~/ComponentState/Clicked, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/EngagementRules/HideButtonC.png' )),
                            //(~/ComponentState/Toggled, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/EngagementRules/HideButtonN.png' )),
                            //(~/ComponentState/Highlighted, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/EngagementRules/HideButtonH.png' )),
                            ] ),

        ("ROE_advance", MAP [ (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/EngagementRules/advance.png' )), ] ),

        ("ROE_pathfind", MAP [ (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/EngagementRules/pathfind.png' )), ] ),

        ("ROE_atgm", MAP [ (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/EngagementRules/atgm.png' )), ] ),
        ("ROE_sell", MAP [ (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/EngagementRules/sell.png' )), ] ),

        ("ROE_outranged", MAP [ (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/EngagementRules/outranged.png' )), ] ),

        ("ROE_unarmed", MAP [ (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/EngagementRules/unarmed.png' )), ] ),

        ("ROE_foot", MAP [ (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/EngagementRules/foot.png' )), ] ),

        ("ROE_idle", MAP [ (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/EngagementRules/idle.png' )), ] ),

        ("ROE_Icone_Radar", MAP [ (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/EngagementRules/radar.png' )), ] ),

        //-------------------------------------------------------------------------------------// icones
        // ⚠ TFS : Ne pas mettre de TFS concernant les unites ou tout autre TFS qui sera visible dans la showroom ⚠
        // Ces autres TFS doivent aller dans CommonTextures.ndf et doivent etre declaree avec un TUIResourceTexture_Common
        ("icone_starting_information", MAP [ (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/Icones/starting_information.png' )),] ),
        ("icone_PourPanelSelectionRdr", MAP [ (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/Icones/rdr.png' )), ] ),
        ("icone_los",       MAP [ (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/Icones/los.png' )),] ),
        ("icone_chat",      MAP [ (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/Icones/chat.png' )),] ),
        ("icone_score",      MAP [
                            (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/Icones/scoreClose.png' )),
                            (~/ComponentState/Toggled, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/Icones/scoreClose.png' )),
                            (~/ComponentState/ToggleHighlighted, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/Icones/scoreClose.png' )),
                            ] ),
        ("icone_roe",      MAP [
                            (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/Icones/roe.png' )),
                            (~/ComponentState/Clicked, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/Icones/roe.png' )),
                            (~/ComponentState/Toggled, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/Icones/roe_clicked.png' )),
                            (~/ComponentState/Highlighted, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/Icones/roe_clicked.png' )),
                            ] ),
        ("icone_help",      MAP [ (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/Icones/los.png' )), ] ),
        ("icone_bullets",      MAP [ (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/Icones/bullets2.png' )), ] ),
        ("icone_bullets_refill",      MAP [ (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/Icones/bullets2_refill.png' )), ] ),
        ("icone_commander",      MAP [ (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/Icones/commander.png' )), ] ),
        ("icone_critical",      MAP [ (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/Icones/critical.png' )), ] ),
        ("icone_evacuation",      MAP [ (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/Icones/evacuation.png' )), ] ),
        ("icone_riposte",      MAP [ (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/Icones/riposte.png' )), ] ),
        ("icone_fuel",      MAP [ (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/Icones/fuel2.png' )), ] ),
        ("icone_fuel_refill",      MAP [ (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/Icones/fuel2_refill.png' )), ] ),
        ("icone_life",      MAP [ (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/Icones/life2.png' )), ] ),
        ("icone_life_refill",      MAP [ (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/Icones/life2_refill.png' )), ] ),
        ("icone_missile",      MAP [ (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/Icones/missile2.png' )), ] ),
        ("icone_missile_refill",      MAP [ (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/Icones/missile2_refill.png' )), ] ),
        ("icone_ifv",      MAP [ (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/Icones/ifv.png' )), ] ),
        ("icone_ifv_off",      MAP [ (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/Icones/ifv_off.png' )), ] ),
        ("icone_resolute",      MAP [ (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/Icones/resolute.png' )), ] ),
        ("icone_leader",      MAP [ (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/Icones/leader.png' )), ] ),
        ("icone_mp",      MAP [ (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/Icones/mp.png' )), ] ),
        ("icone_mp_given",      MAP [ (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/Icones/mp_given.png' )), ] ),
        ("icone_sf",      MAP [ (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/Icones/sf.png' )), ] ),
        ("icone_gsr",      MAP [ (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/Icones/gsr.png' )), ] ),
        ("icone_electronic_warfare",      MAP [ (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/Icones/electronic_warfare.png' )), ] ),
        ("icone_effet_stressOnMiss",      MAP [ (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/Icones/effet_stressOnMiss.png' )), ] ),
        ("icone_radar",      MAP [ (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/Icones/radar.png' )), ] ),
        ("icone_shock",      MAP [ (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/Icones/shock.png' )), ] ),
        ("icone_shock_off",      MAP [ (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/Icones/shock_off.png' )), ] ),
        ("icone_security",      MAP [ (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/Icones/security.png' )), ] ),
        ("icone_reserviste",      MAP [ (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/Icones/reserviste.png' )), ] ),
        ("icone_sniper",      MAP [ (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/Icones/sniper.png' )), ] ),
        ("icone_sniper_off",      MAP [ (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/Icones/sniper_off.png' )), ] ),
        ("icone_sigint_close",      MAP [ (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/Icones/sigint_close.png' )), ] ),
        ("icone_sigint_far",      MAP [ (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/Icones/sigint_far.png' )), ] ),
        ("icone_sigint_off",      MAP [ (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/Icones/sigint_off.png' )), ] ),
        ("icone_fireDirection",      MAP [ (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/Icones/firedirection.png' )), ] ),

        ("icone_panne",      MAP [ (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/Icones/panne.png' )), ] ),

        ("icone_rout",      MAP [ (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/Icones/rout.png' )), ] ),
        ("icone_pinned",      MAP [ (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/Icones/stress.png' )), ] ),
        ("icone_stun",      MAP [ (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/Icones/stun.png' )), ] ),
        ("icone_shell",      MAP [ (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/Icones/shell2.png' )), ] ),
        ("icone_shell_refill",      MAP [ (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/Icones/shell2_refill.png' )), ] ),
        ("icone_stress",      MAP [ (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/Icones/stress.png' )), ] ),
        ("icone_leave",      MAP [ (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/Icones/leave.png' )), ] ),

        ("icone_reticule",      MAP [ (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/Icones/reticule.png' )), ] ),
        ("icone_reticule_vide",      MAP [ (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/Icones/reticule_vide.png' )), ] ),
        ("icone_chrono_district",      MAP [ (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/Icones/chrono_district.png' )), ] ),
        ("icone_chrono_simple",      MAP [ (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/Icones/chrono_simple.png' )), ] ),
        ("icone_idle_unit",      MAP [ (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/Icones/IdleUnit.png' )),
                                       (~/ComponentState/Grayed, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/Icones/IdleUnitGrayed.png' )), ] ),

        //-------------------------------------------------------------------------------------// unit comparator
        ("unitComparatorDisabled", MAP [
                            (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/UnitComparatorSelector/Disabled.png' )),
                            ] ),
        ("unitComparatorStatic", MAP [
                            (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/UnitComparatorSelector/Static.png' )),
                            ] ),
        ("unitComparatorDynamic", MAP [
                            (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/UnitComparatorSelector/Dynamic.png' )),
                            ] ),

        ("transparent", MAP [(~/ComponentState/Grayed, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseStrategic/Icons/transparent.png' )),] ),
        ("Transparent", MAP [(~/ComponentState/Grayed, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseStrategic/Icons/transparent.png' )),] ),

        //-------------------------------------------------------------------------------------// orders
        ("textureOrders", MAP [
                            (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/Icones/orders.png' )),
                            ] ),

        //-------------------------------------------------------------------------------------// offmap
        ("textureArtilleriePrecision", MAP [
                            (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/OffMap/precisionn.png' )),
                            (~/ComponentState/Clicked, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/OffMap/precision.png' )),
                            (~/ComponentState/Highlighted, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/OffMap/precision.png' )),
                            (~/ComponentState/Toggled, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/OffMap/precisiont.png' )),
                            ] ),

        ("textureArtillerieBarrage", MAP [
                            (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/OffMap/barragen.png' )),
                            (~/ComponentState/Clicked, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/OffMap/barrage.png' )),
                            (~/ComponentState/Highlighted, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/OffMap/barrage.png' )),
                            (~/ComponentState/Toggled, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/OffMap/barraget.png' )),
                            ] ),
        ("textureArtillerieAlerte", MAP [
                            (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/OffMap/alerten.png' )),
                            (~/ComponentState/Clicked, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/OffMap/alerte.png' )),
                            (~/ComponentState/Highlighted, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/OffMap/alerte.png' )),
                            (~/ComponentState/Toggled, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/OffMap/alertet.png' )),
                            ] ),
        ("textureArtillerieSmoke", MAP [
                            (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/OffMap/smoken.png' )),
                            (~/ComponentState/Clicked, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/OffMap/smoke.png' )),
                            (~/ComponentState/Highlighted, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/OffMap/smoke.png' )),
                            (~/ComponentState/Toggled, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/OffMap/smoket.png' )),
                            ] ),
        ("textureEvac", MAP [
                            (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/OffMap/evacn.png' )),
                            (~/ComponentState/Clicked, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/OffMap/evac.png' )),
                            (~/ComponentState/Highlighted, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/OffMap/evac.png' )),
                            (~/ComponentState/Toggled, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/OffMap/evact.png' )),
                            ] ),

        //-------------------------------------------------------------------------------------
        ("textureMobilisation", MAP [
                            (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseStrategic/Icons/mobilisationn.png' )),
                            (~/ComponentState/Clicked, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseStrategic/Icons/mobilisation.png' )),
                            (~/ComponentState/Highlighted, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseStrategic/Icons/mobilisation.png' )),
                            (~/ComponentState/Toggled, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseStrategic/Icons/mobilisationt.png' )),
                            (~/ComponentState/Grayed, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseStrategic/Icons/transparent.png' )),
                            ] ),

        ("textureMobilisation_HQ", MAP [
                            (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseStrategic/Icons/mobilisation_HQn.png' )),
                            (~/ComponentState/Clicked, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseStrategic/Icons/mobilisation_HQ.png' )),
                            (~/ComponentState/Highlighted, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseStrategic/Icons/mobilisation_HQ.png' )),
                            (~/ComponentState/Toggled, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseStrategic/Icons/mobilisation_HQt.png' )),
                            (~/ComponentState/Grayed, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseStrategic/Icons/transparent.png' )),
                            ] ),

        ("texture_HQ_Regiment", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseStrategic/Icons/HQ_Regiment.png' ))]),
        ("texture_HQ_Division", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseStrategic/Icons/HQ_Division.png' ))]),
        ("texture_HQ_Corps", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseStrategic/Icons/HQ_Corps.png' ))]),
        ("texture_HQ_Army", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseStrategic/Icons/HQ_Army.png' ))]),
        ("texture_HQ_ArmyGroup", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseStrategic/Icons/HQ_ArmyGroup.png' ))]),

        ("UseInGame_Texture_OffMap_Ammo",                                MAP [(~/ComponentState/Normal, ~/UseInGame_Texture_OffMap_Ammo)] ),

        ("UseInGame_Texture_GameSpeed_Accelerate",                       MAP [
                                                                             (~/ComponentState/Normal, ~/UseInGame_Texture_GameSpeed_Accelerate),
                                                                             (~/ComponentState/Clicked, ~/UseInGame_Texture_GameSpeed_Accelerate_Selected),
                                                                             (~/ComponentState/Toggled, ~/UseInGame_Texture_GameSpeed_Accelerate),
                                                                         ] ),
        ("UseInGame_Texture_GameSpeed_Accelerate_More",                       MAP [
                                                                             (~/ComponentState/Normal, ~/UseInGame_Texture_GameSpeed_Accelerate_More),
                                                                             (~/ComponentState/Clicked, ~/UseInGame_Texture_GameSpeed_Accelerate_More_Selected),
                                                                             (~/ComponentState/Toggled, ~/UseInGame_Texture_GameSpeed_Accelerate_More),
                                                                         ] ),
        ("UseInGame_Texture_GameSpeed_Decelerate",                       MAP [
                                                                             (~/ComponentState/Normal, ~/UseInGame_Texture_GameSpeed_Decelerate),
                                                                             (~/ComponentState/Clicked, ~/UseInGame_Texture_GameSpeed_Decelerate_Selected),
                                                                             (~/ComponentState/Toggled, ~/UseInGame_Texture_GameSpeed_Decelerate),
                                                                         ] ),
        ("UseInGame_Texture_GameSpeed_Play",                             MAP [
                                                                             (~/ComponentState/Normal, ~/UseInGame_Play),
                                                                             (~/ComponentState/Clicked, ~/UseInGame_Play_Selected),
                                                                             (~/ComponentState/Toggled, ~/UseInGame_Play),
                                                                         ] ),
        ("UseInGame_Texture_GameSpeed_Pause",                            MAP [
                                                                             (~/ComponentState/Normal, ~/UseInGame_Pause),
                                                                             (~/ComponentState/Clicked, ~/UseInGame_Pause_Selected),
                                                                             (~/ComponentState/Toggled, ~/UseInGame_Pause),
                                                                         ] ),
        ("UseInGame_Texture_Replay_Accelerate",                            MAP [
                                                                             (~/ComponentState/Normal, ~/UseInGame_Texture_Replay_Accelerate),
                                                                             (~/ComponentState/Highlighted, ~/UseInGame_Texture_Replay_Accelerate_Selected),
                                                                             (~/ComponentState/Clicked, ~/UseInGame_Texture_Replay_Accelerate_Selected),
                                                                         ] ),

         ("UseInGame_Texture_Replay_Decelerate",                            MAP [
                                                                             (~/ComponentState/Normal, ~/UseInGame_Texture_Replay_Decelerate),
                                                                             (~/ComponentState/Highlighted, ~/UseInGame_Texture_Replay_Decelerate_Selected),
                                                                             (~/ComponentState/Clicked, ~/UseInGame_Texture_Replay_Decelerate_Selected),
                                                                         ] ),
         ("UseInGame_Texture_Replay_Play",                            MAP [
                                                                             (~/ComponentState/Normal, ~/UseInGame_Texture_Replay_Play),
                                                                             (~/ComponentState/Clicked, ~/UseInGame_Texture_Replay_Play_Selected),
                                                                             (~/ComponentState/Highlighted, ~/UseInGame_Texture_Replay_Play_Selected),
                                                                             (~/ComponentState/Toggled, ~/UseInGame_Texture_Replay_Play_Toggled),
                                                                             (~/ComponentState/ToggleHighlighted, ~/UseInGame_Texture_Replay_Play_ToggleHighlighted),
                                                                         ] ),
        ("UseInGame_Texture_OffMap_FrappePrecision",                       MAP [
                                                                             (~/ComponentState/Normal, ~/UseInGame_Texture_OffMap_FrappePrecision),
                                                                             (~/ComponentState/Highlighted, ~/UseInGame_Texture_OffMap_FrappePrecision_H),
                                                                             (~/ComponentState/Clicked, ~/UseInGame_Texture_OffMap_FrappePrecision_C),
                                                                             (~/ComponentState/Toggled, ~/UseInGame_Texture_OffMap_FrappePrecision_T),
                                                                         ] ),
        ("UseInGame_Texture_OffMap_FrappeAlerte",                       MAP [
                                                                             (~/ComponentState/Normal, ~/UseInGame_Texture_OffMap_FrappeAlerte),
                                                                             (~/ComponentState/Highlighted, ~/UseInGame_Texture_OffMap_FrappeAlerte_H),
                                                                             (~/ComponentState/Clicked, ~/UseInGame_Texture_OffMap_FrappeAlerte_C),
                                                                             (~/ComponentState/Toggled, ~/UseInGame_Texture_OffMap_FrappeAlerte_T),
                                                                         ] ),
        ("UseInGame_Texture_OffMap_FrappeBarrage",                       MAP [
                                                                             (~/ComponentState/Normal, ~/UseInGame_Texture_OffMap_FrappeBarrage),
                                                                             (~/ComponentState/Highlighted, ~/UseInGame_Texture_OffMap_FrappeBarrage_H),
                                                                             (~/ComponentState/Clicked, ~/UseInGame_Texture_OffMap_FrappeBarrage_C),
                                                                             (~/ComponentState/Toggled, ~/UseInGame_Texture_OffMap_FrappeBarrage_T),
                                                                         ] ),

        ("UseInGame_Texture_OffMap_FrappeSmoke",                       MAP [
                                                                             (~/ComponentState/Normal, ~/UseInGame_Texture_OffMap_FrappeSmoke),
                                                                             (~/ComponentState/Highlighted, ~/UseInGame_Texture_OffMap_FrappeSmoke_H),
                                                                             (~/ComponentState/Clicked, ~/UseInGame_Texture_OffMap_FrappeSmoke_C),
                                                                             (~/ComponentState/Toggled, ~/UseInGame_Texture_OffMap_FrappeSmoke_T),
                                                                         ] ),

        ("UseInGame_Texture_OffMap_Evacuate",                           MAP [
                                                                             (~/ComponentState/Normal, ~/UseInGame_Texture_OffMap_Evacuate),
                                                                             (~/ComponentState/Highlighted, ~/UseInGame_Texture_OffMap_Evacuate_H),
                                                                             (~/ComponentState/Clicked, ~/UseInGame_Texture_OffMap_Evacuate_C),
                                                                             (~/ComponentState/Toggled, ~/UseInGame_Texture_OffMap_Evacuate_T),
                                                                        ]),
        ("UseInGame_SituationAwareness_Enemy_unit_contact",              MAP [(~/ComponentState/Normal, ~/UseInGame_SituationAwareness_Enemy_unit_contact)] ),
        ("UseInGame_SituationAwareness_Enemy_unit_destroyed",            MAP [(~/ComponentState/Normal, ~/UseInGame_SituationAwareness_Enemy_unit_destroyed)] ),
        ("UseInGame_SituationAwareness_New_phase_started",               MAP [(~/ComponentState/Normal, ~/UseInGame_SituationAwareness_New_phase_started)] ),
        ("UseInGame_SituationAwareness_Unit_under_attack",               MAP [(~/ComponentState/Normal, ~/UseInGame_SituationAwareness_Unit_under_attack)] ),
        ("UseInGame_SituationAwareness_Player_unit_destroyed",           MAP [(~/ComponentState/Normal, ~/UseInGame_SituationAwareness_Player_unit_destroyed)] ),
        ("UseInGame_SituationAwareness_Player_unit_captured",            MAP [(~/ComponentState/Normal, ~/UseInGame_SituationAwareness_Player_unit_captured)] ),
        ("UseInGame_SituationAwareness_Flare_Launched",                  MAP [(~/ComponentState/Normal, ~/UseInGame_SituationAwareness_Flare_Launched)] ),
        ("UseInGame_SituationAwareness_Enemy_airplane_contact",          MAP [(~/ComponentState/Normal, ~/UseInGame_SituationAwareness_Enemy_airplane_contact)] ),
        ("UseInGame_SituationAwareness_Enemy_unit_captured",             MAP [(~/ComponentState/Normal, ~/UseInGame_SituationAwareness_Enemy_unit_captured)] ),


        ("UseInGame_PerformanceAlert_CPULoadWarning",                    MAP [(~/ComponentState/Normal, ~/UseInGame_PerformanceAlert_CPULoadWarning)] ),
        ("UseInGame_PerformanceAlert_CPULoadAlert",                      MAP [(~/ComponentState/Normal, ~/UseInGame_PerformanceAlert_CPULoadAlert)] ),
        ("UseInGame_PerformanceAlert_LatencyWarning",                    MAP [(~/ComponentState/Normal, ~/UseInGame_PerformanceAlert_LatencyWarning)] ),
        ("UseInGame_PerformanceAlert_LatencyAlert",                      MAP [(~/ComponentState/Normal, ~/UseInGame_PerformanceAlert_LatencyAlert)] ),
        ("UseInGame_PerformanceAlert_MTFrameWarning",                    MAP [(~/ComponentState/Normal, ~/UseInGame_PerformanceAlert_MTFrameWarning)] ),
        ("UseInGame_PerformanceAlert_MTFrameAlert",                      MAP [(~/ComponentState/Normal, ~/UseInGame_PerformanceAlert_MTFrameAlert)] ),
        ("UseInGame_PerformanceAlert_NetworkDelayWarning",               MAP [(~/ComponentState/Normal, ~/UseInGame_PerformanceAlert_NetworkDelayWarning)] ),
        ("UseInGame_PerformanceAlert_NetworkDelayAlert",                 MAP [(~/ComponentState/Normal, ~/UseInGame_PerformanceAlert_NetworkDelayAlert)] ),


        ("UseInGame_StrategicScenario_Destruction",                   MAP [(~/ComponentState/Normal, ~/UseInGame_StrategicScenario_Destruction)] ),
        ("UseInGame_StrategicScenario_Control",                       MAP [(~/ComponentState/Normal, ~/UseInGame_StrategicScenario_Control)] ),

        ("UseInGame_ScorePanel_Objective",                               MAP [(~/ComponentState/Normal, ~/UseInGame_ScorePanel_Objective)] ),

        ("UseInGame_Texture_HUDEye",                                    MAP [
                                                                             (~/ComponentState/Normal, ~/UseInGame_Icone_HUDEye),
                                                                             (~/ComponentState/Highlighted, ~/UseInGame_Icone_HUDEye_HighLight),
                                                                         ] ),

        ("UseInGame_CommandPoints_Chrono_Fond",                         MAP [(~/ComponentState/Normal, ~/UseInGame_Chrono_Fond)]),
        ("UseInGame_CommandPoints_Chrono_Full",                         MAP [(~/ComponentState/Normal, ~/UseInGame_Chrono_Full)]),
        ("UseInGame_SelectionPanel_LOS",                                MAP [(~/ComponentState/Normal, ~/UseInGame_SelectionPanel_LOS)]),
        ("UseInGame_UnitInfoPanel_DisplayState",                        MAP [(~/ComponentState/Normal, ~/UseInGame_UnitInfoPanel_DisplayState)]),

        ("UseInGame_UnitInfoPanel_fond_panel2",                        MAP [(~/ComponentState/Normal, ~/UseInGame_UnitInfoPanel_fond_panel2)]),
        ("UseInGame_UnitInfoPanel_fond_panel_light",                        MAP [(~/ComponentState/Normal, ~/UseInGame_UnitInfoPanel_fond_panel_light)]),
        ("UseInGame_UnitInfoPanel_foreground_panel",                        MAP [(~/ComponentState/Normal, ~/UseInGame_UnitInfoPanel_foreground_panel)]),

        ("UseInGame_CommandPoints", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseInGame/CommandPoints.png"))] ),
        ("UseInGame_RequisitionTimer",  MAP [(~/ComponentState/Normal, TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseInGame/requisitionTimer.png"))] ),
        ("UseInGame_RequisitionTimer_fond",  MAP [(~/ComponentState/Normal, TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseInGame/requisitionTimer_fond.png"))] ),
        ("UseInGame_Sector",  MAP [(~/ComponentState/Normal, TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseInGame/Icones/Sector.png"))] ),

        ("UseInGame_hidden",  MAP [(~/ComponentState/Normal, TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseInGame/Icones/hidden.png"))] ),

        // -------------------------------------------------------------------------------
        // Selection panel
        // -------------------------------------------------------------------------------

        ("weapon_panel_supply_ammo_texture", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseInGame/Icones/supply_ammo.png"))] ),
        ("weapon_panel_supply_fuel_texture", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseInGame/Icones/supply_fuel.png"))] ),
        ("weapon_panel_supply_repair_texture", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseInGame/Icones/supply_repair.png"))] ),

        // -------------------------------------------------------------------------------
        // Labels
        // -------------------------------------------------------------------------------

        ("UseInGame_RadioFeedback",                                      MAP [(~/ComponentState/Normal, ~/UseInGame_RadioFeedback)]),

        ("UseInGame_Chrono_Fond",                                        MAP [(~/ComponentState/Normal, ~/UseInGame_Chrono_Fond)]),
        ("UseInGame_Chrono_Full",                                        MAP [(~/ComponentState/Normal, ~/UseInGame_Chrono_Full)]),
        ("UseInGame_Evacuation",                                         MAP [(~/ComponentState/Normal, ~/UseInGame_Evacuation)]),

        ("UseInGame_Barre_Moral",                                        MAP [(~/ComponentState/Normal, ~/UseInGame_Barre_Moral)]),
        ("UseInGame_Feedback_Oil",                                       MAP [(~/ComponentState/Normal, ~/UseInGame_Feedback_Oil)]),
        ("UseInGame_Feedback_Bullets",                                   MAP [(~/ComponentState/Normal, ~/UseInGame_Feedback_Bullets)]),
        ("UseInGame_Feedback_Bullets_Refill",                            MAP [(~/ComponentState/Normal, ~/UseInGame_Feedback_Bullets_Refill)]),
        ("UseInGame_Feedback_Shell",                                     MAP [(~/ComponentState/Normal, ~/UseInGame_Feedback_Shell)]),
        ("UseInGame_Feedback_Shell_Refill",                              MAP [(~/ComponentState/Normal, ~/UseInGame_Feedback_Shell_Refill)]),
        ("UseInGame_Feedback_Smoke",                                     MAP [(~/ComponentState/Normal, ~/UseInGame_Feedback_Smoke)]),
        ("UseInGame_Feedback_Smoke_Refill",                              MAP [(~/ComponentState/Normal, ~/UseInGame_Feedback_Smoke_Refill)]),
        ("UseInGame_Feedback_Flamethrower",                              MAP [(~/ComponentState/Normal, ~/UseInGame_Feedback_Flamethrower)]),
        ("UseInGame_Feedback_Flamethrower_Refill",                       MAP [(~/ComponentState/Normal, ~/UseInGame_Feedback_Flamethrower_Refill)]),
        ("UseInGame_Feedback_Grenade",                                   MAP [(~/ComponentState/Normal, ~/UseInGame_Feedback_Grenade)]),
        ("UseInGame_Feedback_Grenade_Refill",                            MAP [(~/ComponentState/Normal, ~/UseInGame_Feedback_Grenade_Refill)]),
        ("UseInGame_Feedback_Artillery",                                 MAP [(~/ComponentState/Normal, ~/UseInGame_Feedback_Artillery)]),
        ("UseInGame_Feedback_Artillery_Refill",                          MAP [(~/ComponentState/Normal, ~/UseInGame_Feedback_Artillery_Refill)]),
        ("UseInGame_Feedback_Repair",                                    MAP [(~/ComponentState/Normal, ~/UseInGame_Feedback_Repair)]),
        ("UseInGame_Feedback_Repair_Refill",                             MAP [(~/ComponentState/Normal, ~/UseInGame_Feedback_Repair_Refill)]),

        ("UseInGame_District",                                           MAP [(~/ComponentState/Normal, ~/UseInGame_District)]),
        ("UseInGame_Empty_Supply",                                       MAP [(~/ComponentState/Normal, ~/UseInGame_Empty_Supply)]),

        //-------------------------------------------------------------------------------------

        ("TextureMapTaille1v1",                              MAP [(~/ComponentState/Normal, ~/TextureMapTaille_1v1)]),
        ("TextureMapTaille2v2",                              MAP [(~/ComponentState/Normal, ~/TextureMapTaille_2v2)]),
        ("TextureMapTaille3v3",                              MAP [(~/ComponentState/Normal, ~/TextureMapTaille_3v3)]),
        ("TextureMapTaille4v4",                              MAP [(~/ComponentState/Normal, ~/TextureMapTaille_4v4)]),

        ("TextureComplexiteCampagne",                        MAP [(~/ComponentState/Normal, ~/TextureComplexiteCampagne)]),
        ("Texture_feuilleMission",                           MAP [(~/ComponentState/Normal, ~/Texture_feuilleMission)]),
        ("whitePaper",                           MAP [(~/ComponentState/Normal, TUIResourceTexture_Common(FileName = 'GameData:/Assets/2D/Interface/UseOutGame/Missions/whitePaper.png')  )]),

        // ⚠ TFS : Ne pas mettre de TFS concernant les unites ou tout autre TFS qui sera visible dans la showroom ⚠
        // Ces autres TFS doivent aller dans CommonTextures.ndf et doivent etre declaree avec un TUIResourceTexture_Common
        ("Icon_ConquestArea",                   MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = 'GameData:/Assets/2D/Interface/UseInGame/Icones/Sector_200t.png' ))]),
        ("Icon_CommandArea",                    MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = 'GameData:/Assets/2D/Interface/UseInGame/Icones/CommandPoints_200Transp.png' ))]),
        ("Texture_Icon_CommandAreaResize",      MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = 'GameData:/Assets/2D/Interface/UseInGame/Area/Command_resize.png' ))]),

        //-------------------------------------------------------------------------------------
        //Tutorials
        ("UseInGame_TutorialsIcons_SizeX12_MouseLeftClick",              MAP [(~/ComponentState/Normal, ~/UseInGame_TutorialsIcons_SizeX12_MouseLeftClick)] ),
        ("UseInGame_TutorialsIcons_SizeX12_MouseRightClick",             MAP [(~/ComponentState/Normal, ~/UseInGame_TutorialsIcons_SizeX12_MouseRightClick)] ),
        ("UseInGame_TutorialsIcons_SizeX12_MouseMiddle",                 MAP [(~/ComponentState/Normal, ~/UseInGame_TutorialsIcons_SizeX12_MouseMiddle)] ),
        ("UseInGame_TutorialsIcons_SizeX12_MouseScrollWheel",            MAP [(~/ComponentState/Normal, ~/UseInGame_TutorialsIcons_SizeX12_MouseScrollWheel)] ),
        ("UseInGame_TutorialsIcons_SizeX12_MouseLeftClickDragAndDrop",   MAP [(~/ComponentState/Normal, ~/UseInGame_TutorialsIcons_SizeX12_MouseLeftClickDragAndDrop)] ),

        //-------------------------------------------------------------------------------------
        // Panel LoS
        ("UseInGame_LoS_Bad",                        MAP [(~/ComponentState/Normal,  TUIResourceTexture_Common( FileName = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/RTS/Armor.png'))] ),
        ("UseInGame_LoS_Mediocre",                        MAP [(~/ComponentState/Normal,  TUIResourceTexture_Common( FileName = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/RTS/appui.png'))] ),
        ("UseInGame_LoS_Good",                        MAP [(~/ComponentState/Normal,  TUIResourceTexture_Common( FileName = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/RTS/Infantry.png'))] ),
        ("UseInGame_LoS_Exceptional",                        MAP [(~/ComponentState/Normal,  TUIResourceTexture_Common( FileName = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/RTS/Infantry_half.png'))] ),
    ]
)

// -------------------------------------------------------------------------------
// Common
// -------------------------------------------------------------------------------
//⚠ Ces textures servent a plusieurs endroits, faire attention au moment de la supression ⚠
UseInGame_Play           is TUIResourceTexture_Common(FileName = 'GameData:/Assets/2D/Interface/UseInGame/Common/play.png')
UseInGame_Play_Selected  is TUIResourceTexture_Common(FileName = 'GameData:/Assets/2D/Interface/UseInGame/Common/play_s.png')
UseInGame_Pause          is TUIResourceTexture_Common(FileName = 'GameData:/Assets/2D/Interface/UseInGame/Common/pause.png')
UseInGame_Pause_Selected is TUIResourceTexture_Common(FileName = 'GameData:/Assets/2D/Interface/UseInGame/Common/pause_s.png')
UseInGame_Chrono_Fond    is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseInGame/LabelIcons/targetLoading.png")
UseInGame_Chrono_Full    is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseInGame/LabelIcons/target.png")

// -------------------------------------------------------------------------------
// Line of Sight
// -------------------------------------------------------------------------------
UseInGame_SelectionPanel_LOS is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseInGame/LOS/LOS.png")

// -------------------------------------------------------------------------------
// Selection Panel
// -------------------------------------------------------------------------------
UseInGame_Icone_HUDEye                      is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/ShowRoom/Hide_Grid.png")
UseInGame_Icone_HUDEye_HighLight            is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/ShowRoom/Hide_Grid_s.png")

// -------------------------------------------------------------------------------
// Score Panel
// -------------------------------------------------------------------------------
UseInGame_ScorePanel_Objective                is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseInGame/Score/objective.png")

// -------------------------------------------------------------------------------
// Minimap Panel
// -------------------------------------------------------------------------------
Texture_Minimap_CameraAngle          is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseInGame/Minimap/CameraAngle.png")
Texture_Minimap_Alert                is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseInGame/Minimap/minimap_alert.png")
Texture_Minimap_Death                is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseInGame/Minimap/minimap_death.png")
Texture_Minimap_Alarm                is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseInGame/Minimap/minimap_alarm.png")

Texture_MinimapFlareAttack           is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseInGame/FlarePanel/attack.png")
Texture_MinimapFlareDefend           is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseInGame/FlarePanel/defense.png")
Texture_MinimapFlareHelp             is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseInGame/FlarePanel/help.png")
Texture_MinimapFlareCustom           is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseInGame/FlarePanel/custom.png")
Texture_MinimapPing_Spotted          is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseInGame/Minimap/minimap_alert.png")
Texture_MinimapFlareFireSupport      is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseInGame/FlarePanel/fireSupport.png")
Texture_MinimapFlareEnemySpotted     is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseInGame/FlarePanel/enemySpotted.png")

// -------------------------------------------------------------------------------
// Off map Panel
// -------------------------------------------------------------------------------
UseInGame_Texture_OffMap_FrappePrecision    is TUIResourceTexture_Common(FileName = 'GameData:/Assets/2D/Interface/UseInGame/OffMap/frappe_1.png')
UseInGame_Texture_OffMap_FrappePrecision_H  is TUIResourceTexture_Common(FileName = 'GameData:/Assets/2D/Interface/UseInGame/OffMap/frappe_1_H.png')
UseInGame_Texture_OffMap_FrappePrecision_C  is TUIResourceTexture_Common(FileName = 'GameData:/Assets/2D/Interface/UseInGame/OffMap/frappe_1_C.png')
UseInGame_Texture_OffMap_FrappePrecision_T  is TUIResourceTexture_Common(FileName = 'GameData:/Assets/2D/Interface/UseInGame/OffMap/frappe_1_T.png')

UseInGame_Texture_OffMap_FrappeAlerte       is TUIResourceTexture_Common(FileName = 'GameData:/Assets/2D/Interface/UseInGame/OffMap/frappe_2.png')
UseInGame_Texture_OffMap_FrappeAlerte_H     is TUIResourceTexture_Common(FileName = 'GameData:/Assets/2D/Interface/UseInGame/OffMap/frappe_2_H.png')
UseInGame_Texture_OffMap_FrappeAlerte_C     is TUIResourceTexture_Common(FileName = 'GameData:/Assets/2D/Interface/UseInGame/OffMap/frappe_2_C.png')
UseInGame_Texture_OffMap_FrappeAlerte_T     is TUIResourceTexture_Common(FileName = 'GameData:/Assets/2D/Interface/UseInGame/OffMap/frappe_2_T.png')

UseInGame_Texture_OffMap_FrappeBarrage      is TUIResourceTexture_Common(FileName = 'GameData:/Assets/2D/Interface/UseInGame/OffMap/frappe_3.png')
UseInGame_Texture_OffMap_FrappeBarrage_H    is TUIResourceTexture_Common(FileName = 'GameData:/Assets/2D/Interface/UseInGame/OffMap/frappe_3_H.png')
UseInGame_Texture_OffMap_FrappeBarrage_C    is TUIResourceTexture_Common(FileName = 'GameData:/Assets/2D/Interface/UseInGame/OffMap/frappe_3_C.png')
UseInGame_Texture_OffMap_FrappeBarrage_T    is TUIResourceTexture_Common(FileName = 'GameData:/Assets/2D/Interface/UseInGame/OffMap/frappe_3_T.png')

UseInGame_Texture_OffMap_Ammo               is TUIResourceTexture_Common(FileName = 'GameData:/Assets/2D/Interface/UseInGame/OffMap/ammo.png')

UseInGame_Texture_OffMap_FrappeSmoke                  is TUIResourceTexture_Common(FileName = 'GameData:/Assets/2D/Interface/UseInGame/OffMap/frappe_4.png')
UseInGame_Texture_OffMap_FrappeSmoke_H                is TUIResourceTexture_Common(FileName = 'GameData:/Assets/2D/Interface/UseInGame/OffMap/frappe_4_H.png')
UseInGame_Texture_OffMap_FrappeSmoke_C                is TUIResourceTexture_Common(FileName = 'GameData:/Assets/2D/Interface/UseInGame/OffMap/frappe_4_C.png')
UseInGame_Texture_OffMap_FrappeSmoke_T                is TUIResourceTexture_Common(FileName = 'GameData:/Assets/2D/Interface/UseInGame/OffMap/frappe_4_T.png')

UseInGame_Texture_OffMap_Evacuate    is TUIResourceTexture_Common(FileName = 'GameData:/Assets/2D/Interface/UseInGame/OffMap/evacuate.png')
UseInGame_Texture_OffMap_Evacuate_H  is TUIResourceTexture_Common(FileName = 'GameData:/Assets/2D/Interface/UseInGame/OffMap/evacuate_H.png')
UseInGame_Texture_OffMap_Evacuate_C  is TUIResourceTexture_Common(FileName = 'GameData:/Assets/2D/Interface/UseInGame/OffMap/evacuate_C.png')
UseInGame_Texture_OffMap_Evacuate_T  is TUIResourceTexture_Common(FileName = 'GameData:/Assets/2D/Interface/UseInGame/OffMap/evacuate_T.png')

// -------------------------------------------------------------------------------
// Unit Info Panel
// -------------------------------------------------------------------------------
UseInGame_UnitInfoPanel_DisplayState is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseInGame/UnitInfos/affichage.png")

UseInGame_UnitInfoPanel_fond_panel2 is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseInGame/UnitInfos/fond_panel2.png")
UseInGame_UnitInfoPanel_fond_panel_light is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseInGame/UnitInfos/fond_panel_light.png")
UseInGame_UnitInfoPanel_foreground_panel is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseInGame/UnitInfos/foreground.png")

UseInGame_UnitInfoPanel_TurnPageRight is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseInGame/UnitInfos/turn_page_right.png" )
UseInGame_UnitInfoPanel_TurnPageLeft is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseInGame/UnitInfos/turn_page_left.png" )

UseInGame_UnitInfoPanel_DefaultVehicle is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseInGame/UnitInfos/veh_default.png" )
UseInGame_UnitInfoPanel_DefaultHelico is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseInGame/UnitInfos/hel_default.png" )
UseInGame_UnitInfoPanel_DefaultInfantry is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseInGame/UnitInfos/infantry_default.png" )


// -------------------------------------------------------------------------------
// Textures de transport
// -------------------------------------------------------------------------------
UseInGame_Transport_ATGM           is TUIResourceTexture_Common( FileName = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/RTS/AT.png')
UseInGame_Transport_COMMAND        is TUIResourceTexture_Common( FileName = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/RTS/HQ.png')
UseInGame_Transport_RECO           is TUIResourceTexture_Common( FileName = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/RTS/reco.png')
UseInGame_Transport_REGINF         is TUIResourceTexture_Common( FileName = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/RTS/Infantry.png')
UseInGame_Transport_LAAD           is TUIResourceTexture_Common( FileName = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/RTS/manpad.png')
UseInGame_Transport_flak           is TUIResourceTexture_Common( FileName = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/RTS/AA.png')
UseInGame_Transport_howitzer       is TUIResourceTexture_Common( FileName = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/RTS/howitzer.png')
UseInGame_Transport_ATGun          is TUIResourceTexture_Common( FileName = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/RTS/ATGun.png')
UseInGame_Transport_Mortar         is TUIResourceTexture_Common( FileName = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/RTS/mortar.png')


// old icones

UseInGame_Transport_SUPPORT        is TUIResourceTexture_Common( FileName = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/Transport/SUPPORT.png')
UseInGame_Transport_FLAME          is TUIResourceTexture_Common( FileName = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/Transport/FLAME.png')
UseInGame_Transport_APC            is TUIResourceTexture_Common( FileName = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/Transport/APC.png')
UseInGame_Transport_ARMORED        is TUIResourceTexture_Common( FileName = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/Transport/armored.png')
UseInGame_Transport_CMD            is TUIResourceTexture_Common( FileName = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/Transport/CMD.png')
UseInGame_Transport_ELIINF         is TUIResourceTexture_Common( FileName = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/Transport/ELIINF.png')
UseInGame_Transport_HOWZ           is TUIResourceTexture_Common( FileName = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/Transport/HOWZ.png')
UseInGame_Transport_supply         is TUIResourceTexture_Common( FileName = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/Transport/supply.png')
UseInGame_Transport_UNKNOW         is TUIResourceTexture_Common( FileName = 'GameData:/Assets/2D/Interface/UseInGame/LabelIcons/Transport/UNKNOW.png')

UseInGame_RadioFeedback                       is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseInGame/LabelIcons/radio_feedback.png")

// -------------------------------------------------------------------------------
// Curseurs tactiques DEBUT
// -------------------------------------------------------------------------------
Texture_Tactical_Cursor_Forbidden           is TUIResourceTexture_Common(FileName = 'GameData:/Assets/2D/Interface/Common/TacticalCursors/Forbidden.png')
Texture_Tactical_Cursor_UnguidedMissile     is TUIResourceTexture_Common(FileName = 'GameData:/Assets/2D/Interface/Common/TacticalCursors/UnguidedMissile.png')
Texture_Tactical_Cursor_Inefficient         is TUIResourceTexture_Common(FileName = 'GameData:/Assets/2D/Interface/Common/TacticalCursors/Inefficient.png')
Texture_Tactical_Cursor_AirMissile          is TUIResourceTexture_Common(FileName = 'GameData:/Assets/2D/Interface/Common/TacticalCursors/AirMissile.png')
Texture_Tactical_Cursor_Artillery           is TUIResourceTexture_Common(FileName = 'GameData:/Assets/2D/Interface/Common/TacticalCursors/Artillery.png')
Texture_Tactical_Cursor_Canon               is TUIResourceTexture_Common(FileName = 'GameData:/Assets/2D/Interface/Common/TacticalCursors/Canon.png')
Texture_Tactical_Cursor_FastCanon           is TUIResourceTexture_Common(FileName = 'GameData:/Assets/2D/Interface/Common/TacticalCursors/FastCanon.png')
Texture_Tactical_Cursor_GroundMissile       is TUIResourceTexture_Common(FileName = 'GameData:/Assets/2D/Interface/Common/TacticalCursors/GroundMissile.png')
Texture_Tactical_Cursor_MG                  is TUIResourceTexture_Common(FileName = 'GameData:/Assets/2D/Interface/Common/TacticalCursors/MG.png')
Texture_Tactical_Cursor_MoveAndExplode      is TUIResourceTexture_Common(FileName = 'GameData:/Assets/2D/Interface/Common/TacticalCursors/MG.png')
Texture_Tactical_Cursor_SuppressFire        is TUIResourceTexture_Common(FileName = 'GameData:/Assets/2D/Interface/Common/TacticalCursors/SuppressFire.png')
Texture_Tactical_Cursor_SelectionPointer    is TUIResourceTexture_Common(FileName = 'GameData:/Assets/2D/Interface/Common/TacticalCursors/SelectionPointer.png')
Texture_Tactical_Cursor_SquarePointer       is TUIResourceTexture_Common(FileName = 'GameData:/Assets/2D/Interface/Common/TacticalCursors/SquarePointer.png')
// -------------------------------------------------------------------------------
// Panneau de vitesse de deroulement du jeu
// -------------------------------------------------------------------------------
UseInGame_Texture_GameSpeed_Decelerate              is TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/GameSpeed/rw.png' )
UseInGame_Texture_GameSpeed_Decelerate_Selected     is TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/GameSpeed/rw_s.png' )
UseInGame_Texture_GameSpeed_Accelerate              is TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/GameSpeed/ff.png' )
UseInGame_Texture_GameSpeed_Accelerate_Selected     is TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/GameSpeed/ff_s.png' )
UseInGame_Texture_GameSpeed_Accelerate_More              is TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/GameSpeed/vff.png' )
UseInGame_Texture_GameSpeed_Accelerate_More_Selected     is TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/GameSpeed/vff_s.png' )


// Panneau de vitesse de deroulement du replay
UseInGame_Texture_Replay_Decelerate             is UseInGame_Texture_GameSpeed_Decelerate
UseInGame_Texture_Replay_Decelerate_Selected    is UseInGame_Texture_GameSpeed_Decelerate_Selected
UseInGame_Texture_Replay_Play                   is UseInGame_Pause
UseInGame_Texture_Replay_Play_Selected          is UseInGame_Pause_Selected
UseInGame_Texture_Replay_Play_Toggled           is UseInGame_Play_Selected
UseInGame_Texture_Replay_Play_ToggleHighlighted is UseInGame_Play_Selected
UseInGame_Texture_Replay_Accelerate             is UseInGame_Texture_GameSpeed_Accelerate
UseInGame_Texture_Replay_Accelerate_Selected    is UseInGame_Texture_GameSpeed_Accelerate_Selected


// -------------------------------------------------------------------------------
// Situation Awareness
// -------------------------------------------------------------------------------
UseInGame_SituationAwareness_Enemy_unit_contact     is TUIResourceTexture_Common(FileName = 'GameData:/Assets/2D/Interface/UseInGame/SituationAwareness/Enemy_unit_contact.png')
UseInGame_SituationAwareness_Enemy_unit_destroyed   is TUIResourceTexture_Common(FileName = 'GameData:/Assets/2D/Interface/UseInGame/SituationAwareness/Enemy_unit_destroyed.png')
UseInGame_SituationAwareness_New_phase_started      is TUIResourceTexture_Common(FileName = 'GameData:/Assets/2D/Interface/UseInGame/SituationAwareness/New_phase_started.png')
UseInGame_SituationAwareness_Unit_under_attack      is TUIResourceTexture_Common(FileName = 'GameData:/Assets/2D/Interface/UseInGame/SituationAwareness/Unit_under_attack.png')
UseInGame_SituationAwareness_Player_unit_destroyed  is TUIResourceTexture_Common(FileName = 'GameData:/Assets/2D/Interface/UseInGame/SituationAwareness/Player_unit_destroyed.png')
UseInGame_SituationAwareness_Player_unit_captured   is TUIResourceTexture_Common(FileName = 'GameData:/Assets/2D/Interface/UseInGame/SituationAwareness/Player_unit_captured.png')
UseInGame_SituationAwareness_Flare_Launched         is TUIResourceTexture_Common(FileName = 'GameData:/Assets/2D/Interface/UseInGame/SituationAwareness/Flare_Launched.png')
UseInGame_SituationAwareness_Enemy_airplane_contact is TUIResourceTexture_Common(FileName = 'GameData:/Assets/2D/Interface/UseInGame/SituationAwareness/Enemy_airplane_contact.png')
UseInGame_SituationAwareness_Enemy_unit_captured    is TUIResourceTexture_Common(FileName = 'GameData:/Assets/2D/Interface/UseInGame/SituationAwareness/Enemy_unit_captured.png')

// -------------------------------------------------------------------------------
// Performance Alert
// -------------------------------------------------------------------------------
UseInGame_PerformanceAlert_CPULoadWarning           is TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/PerformanceAlert/CPULoadWarning.png" )
UseInGame_PerformanceAlert_CPULoadAlert             is TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/PerformanceAlert/CPULoadAlert.png" )

UseInGame_PerformanceAlert_LatencyWarning           is TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/PerformanceAlert/LatencyWarning.png" )
UseInGame_PerformanceAlert_LatencyAlert             is TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/PerformanceAlert/LatencyAlert.png" )

UseInGame_PerformanceAlert_MTFrameWarning           is TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/PerformanceAlert/MTFrameWarning.png" )
UseInGame_PerformanceAlert_MTFrameAlert             is TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/PerformanceAlert/MTFrameAlert.png" )

UseInGame_PerformanceAlert_NetworkDelayWarning      is TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/PerformanceAlert/NetworkDelayWarning.png" )
UseInGame_PerformanceAlert_NetworkDelayAlert        is TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/PerformanceAlert/NetworkDelayAlert.png" )

// -------------------------------------------------------------------------------
// Labels
// -------------------------------------------------------------------------------
UseInGame_Barre_Moral     is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseInGame/LabelIcons/Barre_de_moral.png")

UseInGame_Empty_Supply    is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseInGame/LabelIcons/empty.png")

UseInGame_Feedback_Bullets                is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseInGame/LabelIcons/ammo_bullets_feedback.png")
UseInGame_Feedback_Bullets_Refill         is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseInGame/LabelIcons/ammo_bullets_feedback_refill.png")
UseInGame_Feedback_Shell                  is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseInGame/LabelIcons/ammo_shell_feedback.png")
UseInGame_Feedback_Shell_Refill           is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseInGame/LabelIcons/ammo_shell_feedback_refill.png")
UseInGame_Feedback_Smoke                  is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseInGame/LabelIcons/ammo_smoke_feedback.png")
UseInGame_Feedback_Smoke_Refill           is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseInGame/LabelIcons/ammo_smoke_feedback_refill.png")
UseInGame_Feedback_Flamethrower           is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseInGame/LabelIcons/ammo_flamethrower_feedback.png")
UseInGame_Feedback_Flamethrower_Refill    is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseInGame/LabelIcons/ammo_flamethrower_feedback_refill.png")
UseInGame_Feedback_Grenade                is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseInGame/LabelIcons/ammo_grenade_feedback.png")
UseInGame_Feedback_Grenade_Refill         is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseInGame/LabelIcons/ammo_grenade_feedback_refill.png")
UseInGame_Feedback_Artillery              is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseInGame/LabelIcons/ammo_artillery_feedback.png")
UseInGame_Feedback_Artillery_Refill       is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseInGame/LabelIcons/ammo_artillery_feedback_refill.png")
UseInGame_Feedback_Repair                 is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseInGame/LabelIcons/repair_feedback.png")
UseInGame_Feedback_Repair_Refill          is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseInGame/LabelIcons/repair_feedback_refill.png")
UseInGame_Feedback_Oil                    is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseInGame/LabelIcons/oil_feedback.png")



UseInGame_Evacuation is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseInGame/LabelIcons/evac_feedback.png")

UseInGame_District is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseInGame/LabelIcons/Icone_Quartier.png")

// -------------------------------------------------------------------------------
// Strategic Scenarios
// -------------------------------------------------------------------------------
UseInGame_StrategicScenario_Destruction                   is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseInGame/StrategicScenario/icone_destruction.png")
UseInGame_StrategicScenario_Control                       is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseInGame/StrategicScenario/icone_controle.png")

//-------------------------------------------------------------------------------------//
// nom des map outgame
//-------------------------------------------------------------------------------------//
TextureMapTaille_1v1 is TUIResourceTexture_Common(FileName = 'GameData:/Assets/2D/Interface/UseOutGame/mapTaille1v1.png')
TextureMapTaille_2v2 is TUIResourceTexture_Common(FileName = 'GameData:/Assets/2D/Interface/UseOutGame/mapTaille2v2.png')
TextureMapTaille_3v3 is TUIResourceTexture_Common(FileName = 'GameData:/Assets/2D/Interface/UseOutGame/mapTaille3v3.png')
TextureMapTaille_4v4 is TUIResourceTexture_Common(FileName = 'GameData:/Assets/2D/Interface/UseOutGame/mapTaille4v4.png')

TextureComplexiteCampagne is TUIResourceTexture_Common(FileName = 'GameData:/Assets/2D/Interface/UseOutGame/complex.png')
//-------------------------------------------------------------------------------------//

//-------------------------------------------------------------------------------------
// WARNING ORDER
//-------------------------------------------------------------------------------------
Texture_feuilleMission is TUIResourceTexture_Common(FileName = 'GameData:/Assets/2D/Interface/UseOutGame/Missions/feuille01.png')


//-------------------------------------------------------------------------------------
// tutorials
//-------------------------------------------------------------------------------------
UseInGame_TutorialsIcons_SizeX12_MouseLeftClick              is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseInGame/TutorialsIcons/SizeX12/MouseLeftClick.png")
UseInGame_TutorialsIcons_SizeX12_MouseRightClick             is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseInGame/TutorialsIcons/SizeX12/MouseRightClick.png")
UseInGame_TutorialsIcons_SizeX12_MouseMiddle                 is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseInGame/TutorialsIcons/SizeX12/MouseMiddle.png")
UseInGame_TutorialsIcons_SizeX12_MouseScrollWheel            is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseInGame/TutorialsIcons/SizeX12/MouseScrollWheel.png")
UseInGame_TutorialsIcons_SizeX12_MouseLeftClickDragAndDrop   is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseInGame/TutorialsIcons/SizeX12/MouseLeftClickDragAndDrop.png")
