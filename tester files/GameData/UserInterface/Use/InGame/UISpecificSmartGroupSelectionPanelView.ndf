//----------------------------------------------------

SmartGroupPanelHeadList is BUCKListDescriptor
(
    ElementName = "SmartGroupPanelHeadList"
    ComponentFrame = TUIFramePropertyRTTI()

    Axis = ~/ListAxis/Vertical
    BreadthComputationMode = ~/BreadthComputationMode/ComputeBreadthFromLargestChild

    HasBackground = false
    BackgroundBlockColorToken = "SD2_Noir"

    FirstMargin = TRTTILength(Magnifiable = 4.0)
    InterItemMargin = TRTTILength(Magnifiable = 1.0)
    LastMargin = TRTTILength(Magnifiable = 2.0)

    Elements =
    [
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKTextDescriptor
            (
                ElementName = 'GroupName'
                ComponentFrame = TUIFramePropertyRTTI()

                TextPadding = TRTTILength4(Magnifiable = [10.0, 0.0, 0.0, 0.0])

                ParagraphStyle = ~/paragraphStyleTextLeftAlign

                TextStyle = "Default"

                HorizontalFitStyle = ~/FitStyle/FitToContent
                VerticalFitStyle = ~/FitStyle/FitToContent

                TextColor       = "BlancEquipe"
                TextSize        = "16"
                TextToken       = "SGR_CUSTM"
                TextDico        = ~/LocalisationConstantes/dico_interface_ingame

                TypefaceToken   = "UIMainFont"
            )
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKTextDescriptor
            (
                ElementName = "NbUnits"
                ComponentFrame = TUIFramePropertyRTTI()

                TextPadding = TRTTILength4(Magnifiable = [10.0, 0.0, 0.0, 0.0])
                ParagraphStyle = TParagraphStyle
                (
                    Alignment = UIText_Center
                    VerticalAlignment = UIText_VerticalCenter
                    InterLine = 0
                )

                TextStyle = "Default"
                HorizontalFitStyle = ~/FitStyle/FitToContent
                VerticalFitStyle = ~/FitStyle/FitToContent

                TextToken = "SGR_NBUNT"

                TypefaceToken = "UISecondFont"
                BigLineAction = ~/BigLineAction/CutByDots

                TextDico = ~/LocalisationConstantes/dico_interface_ingame

                TextColor = "Cyan"
                TextSize = "14"
            )
        ),
    ]
)

//----------------------------------------------------

template SmartGroupActionButton
[
    ElementName : string,
    ComponentFrame : TUIFramePropertyRTTI = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [75.0, 25.0]
    ),
    TextToken : string,
    ReinforceBackgroundBlockColorToken : string = "BoutonTemps_Background",
    ReinforceBorderLineColorToken : string = "BoutonTemps_Line",
    IsTogglable : bool,
    HasHintableArea : bool,
    IsClippable : bool = true,
    Mapping : TEugBMutablePBaseClass = nil,
] is BUCKButtonDescriptor
(
    ElementName = <ElementName>

    ComponentFrame = <ComponentFrame>

    IsTogglable = <IsTogglable>
    IsClippable = <IsClippable>

    Mapping = <Mapping>

    Components =
    [
        BUCKContainerDescriptor
        (
            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [1.0, 1.0]
                MagnifiableWidthHeight = [-4.0, -4.0]
                AlignementToFather = [0.5, 0.5]
                AlignementToAnchor = [0.5, 0.5]
            )

            Components =
            [
                PanelRoundedCorner
                (
                    Radius = 3
                    BackgroundBlockColorToken = <ReinforceBackgroundBlockColorToken>
                    BorderLineColorToken = <ReinforceBorderLineColorToken>
                )
            ]
        ),
        BUCKTextDescriptor
        (
            ComponentFrame = TUIFramePropertyRTTI
            (
                AlignementToFather = [0.5, 0.5]
                AlignementToAnchor = [0.5, 0.5]
            )

            ParagraphStyle = TParagraphStyle
            (
                Alignment = UIText_Center
                VerticalAlignment = UIText_VerticalCenter
                InterLine = 0
            )

            TextStyle = "Default"
            HorizontalFitStyle = ~/FitStyle/FitToContent
            VerticalFitStyle = ~/FitStyle/FitToContent

            TextToken = <TextToken>

            TypefaceToken   = "UIMainFont"
            BigLineAction = ~/BigLineAction/BalancedMultiline

            TextDico = ~/LocalisationConstantes/dico_interface_ingame

            TextColor = "ButtonHUD/Text2"
            TextSize  = "12"
        ),
    ] +
    (<HasHintableArea> ?
        [
            BUCKSpecificHintableArea
            (
                ElementName = <ElementName> + "HintableArea"

                HintBodyToken = "SGR_RCOSB"
            ),
        ] : []
    )
)

//----------------------------------------------------

SmartGroupLabelContainer is BUCKContainerDescriptor
(
    ElementName = "SmartGroupLabelContainerAndButton"
    ComponentFrame = TUIFramePropertyRTTI()
    FitStyle = ~/ContainerFitStyle/FitToContent

    Components =
    [
        SmartGroupActionButton
        (
            ElementName = "ReinforceLabelButton"
            ComponentFrame = TUIFramePropertyRTTI
            (
                MagnifiableWidthHeight = [75.0, 25.0]
                AlignementToFather = [0.5, 0.0]
                AlignementToAnchor = [0.5, 1.0]
            )

            ReinforceBackgroundBlockColorToken = "BoutonSelectionMultiple"
            ReinforceBorderLineColorToken = "H2_bleu_2_40p"

            TextToken = "SGR_REIN"
            IsTogglable = false
            HasHintableArea = true
            IsClippable = false
        ),
        BUCKContainerDescriptor
        (
            ElementName = "SmartGroupLabelContainer"
            ComponentFrame = TUIFramePropertyRTTI()
            FitStyle = ~/ContainerFitStyle/FitToContent
            ChildFitToContent = true
        ),
    ]
)

//----------------------------------------------------

SmartGroupUnitGrid is BUCKSpecificScrollingContainerDescriptor
(
    ElementName = "ScrollingContainer"
    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableOffset = [0.0, -5.0]
        AlignementToAnchor = [0.0, 1.0]
        AlignementToFather = [0.0, 1.0]
    )

    ExternalScrollbar = false
    HasVerticalScrollbar = true

    FitStyle = ~/ContainerFitStyle/FitToContent
    FitToMaximumSize = TRTTILength2(Magnifiable = [1100.0, 83.0])

    ScrollStepSize = [0.0, 10.0]
    VerticalScrollbarComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [10.0, 0.0]
        MagnifiableOffset = [-3.0, 0.0]
    )

    Components =
    [
        BUCKGridDescriptor
        (
            ElementName = "SmartGroupUnitGrid"

            MaxElementsPerDimension = [6, 0]

            FirstElementMargin = TRTTILength2(Magnifiable = [10.0, 0.0])
            InterElementMargin = TRTTILength2(Magnifiable = [2.0, 2.0])
            LastElementMargin = TRTTILength2(Magnifiable = [10.0, 0.0])
        )
    ]
)

//----------------------------------------------------

SmartGroupSelectionPanelMainPanel is BUCKListDescriptor
(
    ElementName = "SmartGroupSelectionPanelMainPanel"

    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [0.0, 93.0]
    )

    Axis = ~/ListAxis/Horizontal

    HidePointerEvents = true

    Elements =
    [
        BUCKListElementDescriptor
        (
            ComponentDescriptor = SmartGroupActionButton
            (
                ElementName = "DisolveButton"

                ComponentFrame = TUIFramePropertyRTTI
                (
                    MagnifiableWidthHeight = [75.0, 25.0]
                    MagnifiableOffset = [0.0, -5.0]
                    AlignementToFather = [0.0, 1.0]
                    AlignementToAnchor = [0.0, 1.0]
                )

                Mapping = $/KeyboardOption/Mapping_MergeUnits

                TextToken = "SGR_DISLV"
                IsTogglable = false
                HasHintableArea = false
            )
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = ~/SmartGroupUnitGrid
        ),
    ]

    BackgroundComponents =
    [
        PanelRoundedCorner
        (
            BorderLineColorToken = "H2_bleu_2"
            BackgroundBlockColorToken = "H2_bleu_2_60p"
        ),
        SmartGroupPanelHeadList,
    ]
)

//----------------------------------------------------

template UISpecificSmartGroupSelectionPanelViewDescriptor
[
    MainComponentContainerUniqueName : string,
] is TUISpecificSmartGroupSelectionPanelViewDescriptor
(
    MainComponentDescriptor = ~/SmartGroupSelectionPanelMainPanel

    MainComponentContainerUniqueName = <MainComponentContainerUniqueName> // Permet d'indiquer l'endroit ou le composant doit s'insérer

    SmartGroupLabelContainer = ~/SmartGroupLabelContainer

    UnitLabelResources = ~/SpecificInGameUnitLabelResources
    LabelMagnifier = 0.8
)
