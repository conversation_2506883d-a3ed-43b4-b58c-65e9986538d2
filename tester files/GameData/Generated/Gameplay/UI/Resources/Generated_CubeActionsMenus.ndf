// Ne pas éditer, ce fichier est généré par CubeActionMenuDescriptorFileWriter_Specific


export CubeAction_Menu_Ordres_avions is TCubeActionMenuDescriptor
(
    CubeActionName               = "Ordres_avions"
    CubeActionContent            = MAP[
        ( 1, [ CubeAction_Order_ShootOnPosition ] ),
        ( 2, [ CubeAction_Order_ShootOnPositionSmoke ] ),
        ( 3, [ CubeAction_Order_Move ] ),
        ( 4, [ CubeAction_Order_Attack ] ),
        ( 5, [ CubeAction_Order_Evacuate ] ),
        ( 17, [ CubeAction_Order_AIAirplaneAutoManage ] ),
    ]
)

export CubeAction_Menu_Ordres_helicos is TCubeActionMenuDescriptor
(
    CubeActionName               = "Ordres_helicos"
    CubeActionContent            = MAP[
        ( 0, [ CubeAction_Order_Stop ] ),
        ( 1, [ CubeAction_Order_ShootOnPosition ] ),
        ( 2, [ CubeAction_Order_ChangeAltitude ] ),
        ( 3, [ CubeAction_Order_Move ] ),
        ( 4, [ CubeAction_Order_Attack ] ),
        ( 5, [ CubeAction_Order_UnloadAtPosition ] ),
        ( 8, [ CubeAction_Order_UnloadFromTransport ] ),
        ( 9, [ CubeAction_Order_Land ] ),
        ( 11, [ CubeAction_Order_Sell ] ),
        ( 12, [ CubeAction_Order_AIAttack ] ),
        ( 17, [ CubeAction_Order_AISupply ] ),
    ]
)

export CubeAction_Menu_Ordres_helicos_NonArme is TCubeActionMenuDescriptor
(
    CubeActionName               = "Ordres_helicos_NonArme"
    CubeActionContent            = MAP[
        ( 0, [ CubeAction_Order_Stop ] ),
        ( 1, [ CubeAction_Order_ShootOnPosition ] ),
        ( 2, [ CubeAction_Order_ChangeAltitude ] ),
        ( 3, [ CubeAction_Order_Move ] ),
        ( 4, [ CubeAction_Order_Attack ] ),
        ( 5, [ CubeAction_Order_UnloadAtPosition ] ),
        ( 8, [ CubeAction_Order_UnloadFromTransport ] ),
        ( 9, [ CubeAction_Order_Land ] ),
        ( 11, [ CubeAction_Order_Sell ] ),
        ( 17, [ CubeAction_Order_AISupply ] ),
    ]
)

export CubeAction_Menu_Ordres_infs_simplifie is TCubeActionMenuDescriptor
(
    CubeActionName               = "Ordres_infs_simplifie"
    CubeActionContent            = MAP[
        ( 0, [ CubeAction_Order_Stop ] ),
        ( 1, [ CubeAction_Order_ShootOnPosition ] ),
        ( 2, [ CubeAction_Order_ShootOnPositionSmoke ] ),
        ( 3, [ CubeAction_Order_Move ] ),
        ( 4, [ CubeAction_Order_Attack ] ),
        ( 10, [ CubeAction_Functionality_RiposteStance ] ),
        ( 12, [ CubeAction_Order_AIAttack ] ),
        ( 13, [ CubeAction_Order_AIDefend ] ),
    ]
)

export CubeAction_Menu_Ordres_transports_NonArme is TCubeActionMenuDescriptor
(
    CubeActionName               = "Ordres_transports_NonArme"
    CubeActionContent            = MAP[
        ( 0, [ CubeAction_Order_Stop ] ),
        ( 1, [ CubeAction_Order_ShootOnPosition ] ),
        ( 2, [
                ~/CubeAction_Order_ShootOnPositionSmoke,
                ~/CubeAction_Order_ShootDefensiveSmoke,
             ]),
        ( 3, [ CubeAction_Order_Move ] ),
        ( 4, [ CubeAction_Order_Attack ] ),
        ( 5, [ CubeAction_Order_UnloadAtPosition ] ),
        ( 6, [ CubeAction_Order_QuickMove ] ),
        ( 7, [ CubeAction_Order_FastMoveAndAttack ] ),
        ( 8, [ CubeAction_Order_UnloadFromTransport ] ),
        ( 9, [ CubeAction_Order_Reverse ] ),
        ( 10, [ CubeAction_Functionality_RiposteStance ] ),
        ( 11, [ CubeAction_Order_Sell ] ),
        ( 12, [ CubeAction_Order_AIAttack ] ),
        ( 13, [ CubeAction_Order_AIDefend ] ),
        ( 14, [ CubeAction_Order_AIManageArtillery_Focus ] ),
        ( 15, [ CubeAction_Order_AIManageArtillery_CounterBattery ] ),
        ( 16, [ CubeAction_Order_AIManageArtillery_Auto ] ),
        ( 17, [ CubeAction_Order_AIAirplaneAutoManage ] ),
    ]
)

export CubeAction_Menu_Ordres_unites is TCubeActionMenuDescriptor
(
    CubeActionName               = "Ordres_unites"
    CubeActionContent            = MAP[
        ( 0, [ CubeAction_Order_Stop ] ),
        ( 1, [ CubeAction_Order_ShootOnPosition ] ),
        ( 2, [
                ~/CubeAction_Order_ShootOnPositionSmoke,
                ~/CubeAction_Order_ShootDefensiveSmoke,
             ]),
        ( 3, [ CubeAction_Order_Move ] ),
        ( 4, [ CubeAction_Order_Attack ] ),
        ( 5, [ CubeAction_Order_UnloadAtPosition ] ),
        ( 6, [ CubeAction_Order_QuickMove ] ),
        ( 7, [ CubeAction_Order_FastMoveAndAttack ] ),
        ( 8, [ CubeAction_Order_UnloadFromTransport ] ),
        ( 9, [ CubeAction_Order_Reverse ] ),
        ( 10, [ CubeAction_Functionality_RiposteStance ] ),
        ( 11, [ CubeAction_Order_Sell ] ),
        ( 12, [ CubeAction_Order_AIAttack ] ),
        ( 13, [ CubeAction_Order_AIDefend ] ),
        ( 14, [ CubeAction_Order_AIManageArtillery_Focus ] ),
        ( 15, [ CubeAction_Order_AIManageArtillery_CounterBattery ] ),
        ( 16, [ CubeAction_Order_AIManageArtillery_Auto ] ),
        ( 17, [ CubeAction_Order_AIAirplaneAutoManage ] ),
    ]
)

export CubeAction_Menu_Ordres_unites_NonArme is TCubeActionMenuDescriptor
(
    CubeActionName               = "Ordres_unites_NonArme"
    CubeActionContent            = MAP[
        ( 0, [ CubeAction_Order_Stop ] ),
        ( 1, [ CubeAction_Order_ShootOnPosition ] ),
        ( 2, [
                ~/CubeAction_Order_ShootOnPositionSmoke,
                ~/CubeAction_Order_ShootDefensiveSmoke,
             ]),
        ( 3, [ CubeAction_Order_Move ] ),
        ( 4, [ CubeAction_Order_Attack ] ),
        ( 5, [ CubeAction_Order_UnloadAtPosition ] ),
        ( 6, [ CubeAction_Order_QuickMove ] ),
        ( 7, [ CubeAction_Order_FastMoveAndAttack ] ),
        ( 8, [ CubeAction_Order_UnloadFromTransport ] ),
        ( 9, [ CubeAction_Order_Reverse ] ),
        ( 10, [ CubeAction_Functionality_RiposteStance ] ),
        ( 11, [ CubeAction_Order_Sell ] ),
        ( 15, [ CubeAction_Order_AIManageArtillery_CounterBattery ] ),
        ( 16, [ CubeAction_Order_AIManageArtillery_Auto ] ),
        ( 17, [ CubeAction_Order_AIAirplaneAutoManage ] ),
    ]
)

export CubeAction_Menu_Ordres_vehiculeSol_supply is TCubeActionMenuDescriptor
(
    CubeActionName               = "Ordres_vehiculeSol_supply"
    CubeActionContent            = MAP[
        ( 0, [ CubeAction_Order_Stop ] ),
        ( 1, [ CubeAction_Order_ShootOnPosition ] ),
        ( 2, [ CubeAction_Order_ShootOnPositionSmoke ] ),
        ( 3, [ CubeAction_Order_Move ] ),
        ( 4, [ CubeAction_Order_Attack ] ),
        ( 5, [ CubeAction_Order_UnloadAtPosition ] ),
        ( 6, [ CubeAction_Order_QuickMove ] ),
        ( 7, [ CubeAction_Order_FastMoveAndAttack ] ),
        ( 8, [ CubeAction_Order_UnloadFromTransport ] ),
        ( 9, [ CubeAction_Order_Reverse ] ),
        ( 10, [ CubeAction_Functionality_RiposteStance ] ),
        ( 11, [ CubeAction_Order_Sell ] ),
        ( 17, [ CubeAction_Order_AISupply ] ),
    ]
)

