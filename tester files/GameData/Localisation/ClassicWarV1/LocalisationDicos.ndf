unnamed TLocalisationDicoResource
(
    DicoToken = ~/LocalisationConstantes/dico_units
    FileName = 'GameData:/Localisation/ClassicWarV1/UNITS.csv'
    CanBeMissing = true
)

unnamed TLocalisationDicoResource
(
    DicoToken = ~/LocalisationConstantes/dico_companies
    FileName = 'GameData:/Localisation/ClassicWarV1/COMPANIES.csv'
    CanBeMissing = true
)

unnamed TLocalisationDicoResource
(
    DicoToken = ~/LocalisationConstantes/dico_platoons
    FileName = 'GameData:/Localisation/ClassicWarV1/PLATOONS.csv'
    CanBeMissing = true
)

unnamed TLocalisationDicoResource
(
    DicoToken = ~/LocalisationConstantes/dico_interface_ingame
    FileName = 'GameData:/Localisation/ClassicWarV1/INTERFACE_INGAME.csv'
    CanBeMissing = true
)

unnamed TLocalisationDicoResource
(
    DicoToken = ~/LocalisationConstantes/dico_interface_outgame
    FileName = 'GameData:/Localisation/ClassicWarV1/INTERFACE_OUTGAME.csv'
    CanBeMissing = true
)

