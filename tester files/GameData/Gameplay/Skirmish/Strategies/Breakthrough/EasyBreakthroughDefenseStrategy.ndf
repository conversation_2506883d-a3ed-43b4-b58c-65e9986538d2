//-------------------------------------------------------------------------
//----------------------------- STRATEGY ----------------------------------
//-------------------------------------------------------------------------
export EasyBreakthroughDefenseStrategy is TIAGeneralStrategy
(
    StrategyName = "IziBTDefAI"
    FirstGenerator = EasyBTDef_Choose_Strategy
    TransitionList =
    [
        EasyBTDef_Transition_StartToDeploy,
        EasyBTDef_Transition_StartToCommand,
        EasyBTDef_Transition_DeployToCommand,
    ]
)

//-------------------------------------------------------------------------
//---------------------------- CONDITIONS ---------------------------------
//-------------------------------------------------------------------------
export EasyBTDef_Condition_GotoEasy is TSequenceCondition_True
(
)
//-------------------------------------------------------------------------
export EasyBTDef_Condition_CommandPhase is TSequenceCondition_Phase
(
    PhaseType = CommandPhase
)
//-------------------------------------------------------------------------
//--------------------------- TRANSITIONS ---------------------------------
//-------------------------------------------------------------------------
export EasyBTDef_Transition_StartToDeploy is TIAGeneralStrategyTransition
(
    Origine = EasyBTDef_Choose_Strategy
    Condition = EasyBTDef_Condition_GotoEasy
    Destination = EasyBTDef_Deploiement
)
//-------------------------------------------------------------------------
export EasyBTDef_Transition_StartToCommand is TIAGeneralStrategyTransition
(
    Origine = EasyBTDef_Choose_Strategy
    Condition = EasyBTDef_Condition_CommandPhase
    Destination = EasyBTDef_Command
)
//-------------------------------------------------------------------------
export EasyBTDef_Transition_DeployToCommand is TIAGeneralStrategyTransition
(
    Origine = EasyBTDef_Deploiement
    Condition = EasyBTDef_Condition_CommandPhase
    Destination = EasyBTDef_Command
)

//-------------------------------------------------------------------------
//------------------------------ PHASES -----------------------------------
//-------------------------------------------------------------------------
export EasyBTDef_Choose_Strategy is TSequenceGeneratorDescriptor
(
    // Fake phase qui permet juste d'orienter la strategie dans la direction voulue
    GeneratorList =
    [
        ~/Supply_Skirmish_WithAllied_OnePerCorridor,
    ]
)
//-------------------------------------------------------------------------
export EasyBTDef_Deploiement is TSequenceGeneratorDescriptor
(
    GeneratorList =
    [

        // Capture de zones
        ~/CaptureAllZoneObjectif,


        ~/FOB_Skirmish,

        ~/Defense_Static_ATGM_BKT,
        ~/Defense_Static_ATGM_Reco_BKT,
        ~/Defense_Static_Inf_BKT,

        ~/Supply_Skirmish_WithAllied_OnePerCorridor,

        ~/Defense_Objectif_AA_BKT,

        ~/Defense_Objectif_BKT,
        ~/Defense_Objectif_BKT,
        ~/Defense_Objectif_BKT,

        ~/Reco,

        ~/Airstrike_SEAD,
        ~/Artillery_Expensive,
        ~/Airstrike_Assault,
        ~/Airstrike_AA,
        ~/Artillery_TriggerHappy_1for4Corridors,
    ]

    //------------------------------
    // Scaling
    //------------------------------
    ScalingGeneratorList = ~/BKTDef_Scaling
)
//-------------------------------------------------------------------------
export EasyBTDef_Command is TSequenceGeneratorDescriptor
(
    GeneratorList =
    [
        // Capture de zones
        ~/CaptureAllZoneObjectif,


        ~/Defense_Static_ATGM_BKT,
        ~/Defense_Static_ATGM_Reco_BKT,
        ~/Defense_Static_Inf_BKT,

        ~/Supply_Skirmish_WithAllied_OnePerCorridor,

        ~/Defense_Objectif_AA_BKT,

        ~/Defense_Objectif_BKT,
        ~/Defense_Objectif_BKT,
        ~/Defense_Objectif_BKT,

        ~/Reco,

        ~/Airstrike_SEAD,
        ~/Artillery_Expensive,
        ~/Airstrike_Assault,
        ~/Airstrike_AA,
        ~/Artillery_TriggerHappy_1for4Corridors,
    ]

    //------------------------------
    // Scaling
    //------------------------------
    ScalingGeneratorList = ~/BKTDef_Scaling
)
