//----------------------------------------------------------------------------------------------------------------------------------------
//-----------------------------------------PYLONES D'EMPORT de ROQUETTES et MISSILES par DEFAUT-------------------------------------------
//----------------------------------------------------------------------------------------------------------------------------------------
//Ci-dessous, declaration des meshes de pylones qui sont sur les avions et helicos.
//C'est sur ces meshes que sont ensuite attaches les MISSILES et BOMBES.
template PylonDepictionCommon [ ModelResource ] is TDepictionDescriptor
(
    Selector = SpecificPylonDepictionSelector
    DepictionAlternatives = [ DepictionVisual_LOD_High( MeshDescriptor = <ModelResource> ) ]
)

template PylonDepictionShowroom [ ModelResource ] is TDepictionDescriptor
(
    Selector = OnlyHighDepictionSelector
    DepictionAlternatives = [ DepictionVisual_LOD_High( MeshDescriptor = <ModelResource> ) ]
)

PylonSet_DefaultMissileVehicle is TPylonSet
(
    Pylons = [
        PylonDepictionCommon( ModelResource = $/GFX/DepictionResources/ModelPylon1 ),
        PylonDepictionCommon( ModelResource = $/GFX/DepictionResources/ModelPylon2 ),
        PylonDepictionCommon( ModelResource = $/GFX/DepictionResources/ModelPylon3 ),
        PylonDepictionCommon( ModelResource = $/GFX/DepictionResources/ModelPylon4 ),
        PylonDepictionCommon( ModelResource = $/GFX/DepictionResources/ModelPylon5 ),
        PylonDepictionCommon( ModelResource = $/GFX/DepictionResources/ModelPylon6 ),
        PylonDepictionCommon( ModelResource = $/GFX/DepictionResources/ModelPylon7 ),
    ]
)

PylonSet_DefaultMissileAirplane is TPylonSet
(
    Pylons = [
        PylonDepictionCommon( ModelResource = $/GFX/DepictionResources/ModelPylon1 ),
        PylonDepictionCommon( ModelResource = $/GFX/DepictionResources/ModelPylon2 ),
        PylonDepictionCommon( ModelResource = $/GFX/DepictionResources/ModelPylon3 ),
        PylonDepictionCommon( ModelResource = $/GFX/DepictionResources/ModelPylon4 ),
        PylonDepictionCommon( ModelResource = $/GFX/DepictionResources/ModelPylon5 ),
        PylonDepictionCommon( ModelResource = $/GFX/DepictionResources/ModelPylon6 ),
        PylonDepictionCommon( ModelResource = $/GFX/DepictionResources/ModelPylon7 ),
    ]
)

PylonSet_DefaultMissileHelico is TPylonSet
(
    Pylons = [
        PylonDepictionCommon( ModelResource = $/GFX/DepictionResources/ModelPylon1 ),
        PylonDepictionCommon( ModelResource = $/GFX/DepictionResources/ModelPylon2 ),
        PylonDepictionCommon( ModelResource = $/GFX/DepictionResources/ModelPylon3 ),
        PylonDepictionCommon( ModelResource = $/GFX/DepictionResources/ModelPylon4 ),
        PylonDepictionCommon( ModelResource = $/GFX/DepictionResources/ModelPylon5 ),
        PylonDepictionCommon( ModelResource = $/GFX/DepictionResources/ModelPylon6 ),
        PylonDepictionCommon( ModelResource = $/GFX/DepictionResources/ModelPylon7 ),
    ]
)

//Ci-dessous, declaration des meshes de pylones de ROQUETTES qui sont sur les avions et helicos.
PylonSet_DefaultPodVehicle is TPylonSet
(
    Pylons = [
        PylonDepictionCommon( ModelResource = $/GFX/DepictionResources/PodModelPylon4 ),
        PylonDepictionCommon( ModelResource = $/GFX/DepictionResources/PodModelPylon7 ),
        PylonDepictionCommon( ModelResource = $/GFX/DepictionResources/PodModelPylon19 ),
    ]
)

PylonSet_DefaultPodAirplane is TPylonSet
(
    Pylons = [
        PylonDepictionCommon( ModelResource = $/GFX/DepictionResources/PodModelPylon4 ),
        PylonDepictionCommon( ModelResource = $/GFX/DepictionResources/PodModelPylon6 ),
        PylonDepictionCommon( ModelResource = $/GFX/DepictionResources/PodModelPylon7 ),
        PylonDepictionCommon( ModelResource = $/GFX/DepictionResources/PodModelPylon19 ),
    ]
)

PylonSet_DefaultPodHelico is TPylonSet
(
    Pylons = [
        PylonDepictionCommon( ModelResource = $/GFX/DepictionResources/PodModelPylon4 ),
        PylonDepictionCommon( ModelResource = $/GFX/DepictionResources/PodModelPylon7 ),
        PylonDepictionCommon( ModelResource = $/GFX/DepictionResources/PodModelPylon19 ),
    ]
)

PylonSet_DefaultMissileShowroom is TPylonSet
(
    Pylons = [
        PylonDepictionShowroom( ModelResource = $/GFX/DepictionResources/ModelPylon1 ),
        PylonDepictionShowroom( ModelResource = $/GFX/DepictionResources/ModelPylon2 ),
        PylonDepictionShowroom( ModelResource = $/GFX/DepictionResources/ModelPylon3 ),
        PylonDepictionShowroom( ModelResource = $/GFX/DepictionResources/ModelPylon4 ),
        PylonDepictionShowroom( ModelResource = $/GFX/DepictionResources/ModelPylon5 ),
        PylonDepictionShowroom( ModelResource = $/GFX/DepictionResources/ModelPylon6 ),
        PylonDepictionShowroom( ModelResource = $/GFX/DepictionResources/ModelPylon7 ),
    ]
)

PylonSet_DefaultPodShowroom is TPylonSet
(
    Pylons = [
        PylonDepictionShowroom( ModelResource = $/GFX/DepictionResources/PodModelPylon4 ),
        PylonDepictionShowroom( ModelResource = $/GFX/DepictionResources/PodModelPylon6 ),
        PylonDepictionShowroom( ModelResource = $/GFX/DepictionResources/PodModelPylon7 ),
        PylonDepictionShowroom( ModelResource = $/GFX/DepictionResources/PodModelPylon19 ),
    ]
)

DepictionPylonSet_Default_Showroom is TMultiPylonSet
(
    DefaultPylonSet = PylonSet_DefaultMissileShowroom
    PodPylonSet = PylonSet_DefaultPodShowroom
    BombPylonSet = PylonSet_DefaultMissileShowroom
)
DepictionPylonSet_Vehicle_Default_Showroom is DepictionPylonSet_Default_Showroom
DepictionPylonSet_Airplane_Default_Showroom is DepictionPylonSet_Default_Showroom
DepictionPylonSet_Helico_Default_Showroom is DepictionPylonSet_Default_Showroom

DepictionPylonSet_Vehicle_Default is TMultiPylonSet
(
    DefaultPylonSet = PylonSet_DefaultMissileVehicle
    PodPylonSet = PylonSet_DefaultPodVehicle
    BombPylonSet = PylonSet_DefaultMissileVehicle
)

DepictionPylonSet_Airplane_Default is TMultiPylonSet
(
    DefaultPylonSet = PylonSet_DefaultMissileAirplane
    PodPylonSet = PylonSet_DefaultPodAirplane
    BombPylonSet = PylonSet_DefaultMissileAirplane
)

DepictionPylonSet_Helico_Default is TMultiPylonSet
(
    DefaultPylonSet = PylonSet_DefaultMissileHelico
    PodPylonSet = PylonSet_DefaultPodHelico
    BombPylonSet = PylonSet_DefaultMissileHelico
)

//----------------------------------------------------------------------------------------------------------------------------------------
//---------------------------------------FIN PYLONES D'EMPORT de ROQUETTES et MISSILES par DEFAUT-----------------------------------------
//----------------------------------------------------------------------------------------------------------------------------------------

//Ci-dessous, declaration des meshes de pylones de ROQUETTES qui sont sur les avions et helicos SOV
PylonSet_SOVPodHelico is TPylonSet
(
    Pylons = [
        PylonDepictionCommon( ModelResource = $/GFX/DepictionResources/PodSOVModelPylon_5  ),
        PylonDepictionCommon( ModelResource = $/GFX/DepictionResources/PodSOVModelPylon_16 ),
        PylonDepictionCommon( ModelResource = $/GFX/DepictionResources/PodSOVModelPylon_17 ),
        PylonDepictionCommon( ModelResource = $/GFX/DepictionResources/PodSOVModelPylon_20 ),
        PylonDepictionCommon( ModelResource = $/GFX/DepictionResources/PodSOVModelPylon_32 ),
        PylonDepictionCommon( ModelResource = $/GFX/DepictionResources/PodSOVModelPylon_33 ),
    ]
)

PylonSet_SOVPodAirplane is TPylonSet
(
    Pylons = [
        PylonDepictionCommon( ModelResource = $/GFX/DepictionResources/PodSOVModelPylon_5 ),
        PylonDepictionCommon( ModelResource = $/GFX/DepictionResources/PodSOVModelPylon_16 ),
        PylonDepictionCommon( ModelResource = $/GFX/DepictionResources/PodSOVModelPylon_17 ),
        PylonDepictionCommon( ModelResource = $/GFX/DepictionResources/PodSOVModelPylon_Airplane_20 ),
        PylonDepictionCommon( ModelResource = $/GFX/DepictionResources/PodSOVModelPylon_32 ),
        PylonDepictionCommon( ModelResource = $/GFX/DepictionResources/PodSOVModelPylon_33 ),
    ]
)

PylonSet_SOVPodAirplaneShowroom is TPylonSet
(
    Pylons = [
        PylonDepictionShowroom( ModelResource = $/GFX/DepictionResources/PodSOVModelPylon_5 ),
        PylonDepictionShowroom( ModelResource = $/GFX/DepictionResources/PodSOVModelPylon_16 ),
        PylonDepictionShowroom( ModelResource = $/GFX/DepictionResources/PodSOVModelPylon_17 ),
        PylonDepictionShowroom( ModelResource = $/GFX/DepictionResources/PodSOVModelPylon_Airplane_20 ),
        PylonDepictionShowroom( ModelResource = $/GFX/DepictionResources/PodSOVModelPylon_32 ),
        PylonDepictionShowroom( ModelResource = $/GFX/DepictionResources/PodSOVModelPylon_33 ),
    ]
)

PylonSet_SOVPodHelicoShowroom is TPylonSet
(
    Pylons = [
        PylonDepictionShowroom( ModelResource = $/GFX/DepictionResources/PodSOVModelPylon_5 ),
        PylonDepictionShowroom( ModelResource = $/GFX/DepictionResources/PodSOVModelPylon_16 ),
        PylonDepictionShowroom( ModelResource = $/GFX/DepictionResources/PodSOVModelPylon_17 ),
        PylonDepictionShowroom( ModelResource = $/GFX/DepictionResources/PodSOVModelPylon_20 ),
        PylonDepictionShowroom( ModelResource = $/GFX/DepictionResources/PodSOVModelPylon_32 ),
        PylonDepictionShowroom( ModelResource = $/GFX/DepictionResources/PodSOVModelPylon_33 ),
    ]
)

template PylonSet_PodAirplane [ ModelResource ] is TPylonSet
(
    Pylons = [ PylonDepictionCommon( ModelResource = <ModelResource> ) ]
)

template PylonSet_PodAirplaneShowroom [ ModelResource ] is TPylonSet
(
    Pylons = [ PylonDepictionShowroom( ModelResource = <ModelResource> ) ]
)

DepictionPylonSet_Helico_SOV is TMultiPylonSet
(
    DefaultPylonSet = PylonSet_DefaultMissileHelico
    PodPylonSet = PylonSet_SOVPodHelico
    BombPylonSet = PylonSet_DefaultMissileHelico
)

DepictionPylonSet_Airplane_SOV is TMultiPylonSet
(
    DefaultPylonSet = PylonSet_DefaultMissileAirplane
    PodPylonSet = PylonSet_SOVPodAirplane
    BombPylonSet = PylonSet_DefaultMissileAirplane
)

DepictionPylonSet_SOV_Airplane_Showroom is TMultiPylonSet
(
    DefaultPylonSet = PylonSet_DefaultMissileShowroom
    PodPylonSet = PylonSet_SOVPodAirplaneShowroom
    BombPylonSet = PylonSet_DefaultMissileShowroom
)

DepictionPylonSet_SOV_Helico_Showroom is TMultiPylonSet
(
    DefaultPylonSet = PylonSet_DefaultMissileShowroom
    PodPylonSet = PylonSet_SOVPodHelicoShowroom
    BombPylonSet = PylonSet_DefaultMissileShowroom
)

DepictionPylonSet_Helico_SOV_Showroom is DepictionPylonSet_SOV_Helico_Showroom
DepictionPylonSet_Airplane_SOV_Showroom is DepictionPylonSet_SOV_Airplane_Showroom

// Airplanes
template DepictionPylonSet_Airplane_Common [ ModelResource ] is TMultiPylonSet
(
    DefaultPylonSet = PylonSet_DefaultMissileAirplane
    PodPylonSet = PylonSet_PodAirplane( ModelResource = <ModelResource> )
    BombPylonSet = PylonSet_DefaultMissileAirplane
)

template DepictionPylonSet_Airplane_Common_Showroom [ ModelResource ] is TMultiPylonSet
(
    DefaultPylonSet = PylonSet_DefaultMissileShowroom
    PodPylonSet = PylonSet_PodAirplaneShowroom( ModelResource = <ModelResource> )
    BombPylonSet = PylonSet_DefaultMissileShowroom
)

DepictionPylonSet_Airplane_FR is TMultiPylonSet
(
    DefaultPylonSet = PylonSet_DefaultMissileAirplane
    PodPylonSet = PylonSet_FRPodAirplane
    BombPylonSet = PylonSet_DefaultMissileAirplane
)

DepictionPylonSet_Airplane_FR_Showroom is TMultiPylonSet
(
    DefaultPylonSet = PylonSet_DefaultMissileShowroom
    PodPylonSet = PylonSet_FRPodAirplaneShowroom
    BombPylonSet = PylonSet_DefaultMissileShowroom
)

PylonSet_FRPodAirplane is TPylonSet
(
    Pylons = [
        PylonDepictionCommon( ModelResource = $/GFX/DepictionResources/PodFRModelPylon_4 ),
        PylonDepictionCommon( ModelResource = $/GFX/DepictionResources/PodFRModelResource ),
    ]
)

PylonSet_FRPodAirplaneShowroom is TPylonSet
(
    Pylons = [
        PylonDepictionShowroom( ModelResource = $/GFX/DepictionResources/PodFRModelPylon_4 ),
        PylonDepictionShowroom( ModelResource = $/GFX/DepictionResources/PodFRModelResource ),
    ]
)

// DepictionPylonSet_Airplane_FR is DepictionPylonSet_Airplane_Common( ModelResource = $/GFX/DepictionResources/PodFRModelResource )
// DepictionPylonSet_Airplane_FR_Showroom is DepictionPylonSet_Airplane_Common_Showroom( ModelResource = $/GFX/DepictionResources/PodFRModelResource )

DepictionPylonSet_Airplane_RFA is DepictionPylonSet_Airplane_Common( ModelResource = $/GFX/DepictionResources/PodRFAModelResource )
DepictionPylonSet_Airplane_RFA_Showroom is DepictionPylonSet_Airplane_Common_Showroom( ModelResource = $/GFX/DepictionResources/PodRFAModelResource )

DepictionPylonSet_Airplane_UK is DepictionPylonSet_Airplane_Common( ModelResource = $/GFX/DepictionResources/PodUKModelResource )
DepictionPylonSet_Airplane_UK_Showroom is DepictionPylonSet_Airplane_Common_Showroom( ModelResource = $/GFX/DepictionResources/PodUKModelResource )

DepictionPylonSet_Airplane_CAN is DepictionPylonSet_Airplane_Common( ModelResource = $/GFX/DepictionResources/PodCANModelResource )
DepictionPylonSet_Airplane_CAN_Showroom is DepictionPylonSet_Airplane_Common_Showroom( ModelResource = $/GFX/DepictionResources/PodCANModelResource )

DepictionPylonSet_Airplane_US is DepictionPylonSet_Airplane_Common( ModelResource = $/GFX/DepictionResources/PodUSModelResource )
DepictionPylonSet_Airplane_US_Showroom is DepictionPylonSet_Airplane_Common_Showroom( ModelResource = $/GFX/DepictionResources/PodUSModelResource )

//----------------------------------------------------------------------------------------------------------------------------------------
//------------------------------------PYLONES D'EMPORT de ROQUETTES et MISSILES SPECIFIQUES par UNITES------------------------------------
//----------------------------------------------------------------------------------------------------------------------------------------
//Exemple Ajout d'un pod ATGM

template DepictionPylonSet_ATGM_Template [ Pylon ] is TMultiPylonSet
(
    DefaultPylonSet = TPylonSet( Pylons = [ <Pylon> ] )
)

DepictionPylonSet_ATGM_Showroom is DepictionPylonSet_ATGM_Template
(
    Pylon = PylonDepictionShowroom ( ModelResource = $/GFX/DepictionResources/ATGMModelResource )
)

DepictionPylonSet_ATGM is DepictionPylonSet_ATGM_Template
(
    Pylon = PylonDepictionCommon ( ModelResource = $/GFX/DepictionResources/ATGMModelResource )
)
