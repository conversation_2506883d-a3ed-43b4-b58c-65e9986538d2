// Note : les chaines sont dans le dico INTERFACE_INGAME
private WargameZoneNames is TZoneNamesDico
(
    CampToZoneNames = MAP[
        (0, [
            "ZONEOTAN_A",
            "ZON<PERSON>OTAN_B",
            "ZONEOTAN_C",
            "ZON<PERSON><PERSON><PERSON>_<PERSON>",
            "ZON<PERSON><PERSON>AN_E",
            "ZON<PERSON>OTAN_F",
            "ZONEOTAN_G",
            "ZONEOTAN_H",
            "ZONEOTAN_I",
            "ZONEOTAN_J",
            "ZONEOTAN_K",
            "ZONEOTAN_L",
            "ZON<PERSON>OTAN_M",
            "ZON<PERSON>OTAN_N",
            "ZON<PERSON><PERSON><PERSON>_O",
            "<PERSON><PERSON><PERSON><PERSON><PERSON>_P",
            "<PERSON><PERSON><PERSON>OTAN_Q",
            "<PERSON>ON<PERSON><PERSON><PERSON>_R",
            "ZON<PERSON><PERSON><PERSON>_S",
            "ZON<PERSON>OTAN_T",
            "ZONEOTAN_U",
            "ZON<PERSON>OTAN_V",
            "ZON<PERSON>OTAN_W",
            "ZONEOTAN_X",
            "ZON<PERSON>OTAN_Y",
            "ZON<PERSON><PERSON><PERSON>_Z",
        ]),
        (1, [
            "<PERSON><PERSON>EPACT_A",
            "<PERSON><PERSON>EP<PERSON>T_B",
            "<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>",
            "<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>",
            "<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>",
            "<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>",
            "ZONEPACT_G",
            "ZONEPACT_H",
            "ZONEPACT_I",
            "ZONEPACT_J",
            "ZONEPACT_K",
            "ZONEPACT_L",
            "ZONEPACT_M",
            "ZONEPACT_N",
            "ZONEPACT_O",
            "ZONEPACT_P",
            "ZONEPACT_Q",
            "ZONEPACT_R",
            "ZONEPACT_S",
            "ZONEPACT_T",
            "ZONEPACT_U",
            "ZONEPACT_V",
            "ZONEPACT_W",
            "ZONEPACT_X",
            "ZONEPACT_Y",
            "ZONEPACT_Z",
        ]),
    ]
)

private deploiementZoneManagerDescriptor is TDeploiementZoneManagerDescriptor
(
    AreaManagerProxy = $/M3D/Scene/DeploymentZoneManagerProxy
    ZoneNamesDico = WargameZoneNames
)

private commandementZoneManagerDescriptor is TCommandementZoneManagerDescriptor
(
    AreaManagerProxy = $/M3D/Scene/CommandZoneManagerProxy
    ZoneNamesDico = WargameZoneNames
)

private zoneManagerForIAStratDescriptor is TZoneManagerForIAStratDescriptor
(
    AreaManagerProxy = $/M3D/Scene/IAStratZoneManagerProxy
)

private strategicZoneManagerForIAStratDescriptor is TStrategicIAZoneManagerDescriptor
(
    AreaManagerProxy = $/M3D/Scene/IAStratZoneManagerProxy
)
