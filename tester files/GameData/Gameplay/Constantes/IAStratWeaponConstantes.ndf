
// CONSTANTES POUR LE GAME DESIGN

export IAStratWeaponConstantes is TIAStratWeaponConstantesDescriptor
(

    // GunThreatInfMap
    SafeDamageType = [DamageFamily_suppress, DamageFamily_suppressap, DamageFamily_cluster, DamageFamily_bombe, DamageFamily_cluster_ap]

    DamageWithMenaceForDangerounessArmor = [DamageFamily_he, DamageFamily_fmballe, DamageFamily_balleaa, DamageFamily_balledca, DamageFamily_missile_he, DamageFamily_ap, DamageFamily_ap_missile, DamageFamily_roquette_ap]
    AlwaysThreateningDamageFamily = [DamageFamily_flamme]
    MacroActionArtilleryDamageTypeThreat = [DamageFamily_he]
    AttackDamageTypeThreat = [DamageFamily_he, DamageFamily_balleaa, DamageFamily_balled<PERSON>, DamageFamily_fmballe]
    DamageTypeForPrincipalAmmo = [DamageFamily_he, <PERSON>age<PERSON><PERSON><PERSON>_ap]
)
