#
# Copyright 2019 FormDev Software GmbH
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

#
# This file is loaded for all light themes (that extend class FlatLightLaf).
#
# Documentation:
#  - https://www.formdev.com/flatlaf/properties-files/
#  - https://www.formdev.com/flatlaf/how-to-customize/
#
# NOTE: Avoid copying the whole content of this file to own properties files.
#       This will make upgrading to newer FlatLaf versions complex and error-prone.
#       Instead, copy and modify only those properties that you need to alter.
#

# Colors and style mostly based on IntelliJ theme from IntelliJ IDEA Community Edition,
# which is licensed under the Apache 2.0 license. Copyright 2000-2019 JetBrains s.r.o.
# See: https://github.com/JetBrains/intellij-community/

#---- variables ----

# general background and foreground (text color)
@background = #f2f2f2
@foreground = #000
@disabledBackground = @background
@disabledForeground = tint(@foreground,50%)

# component background
@buttonBackground = lighten(@background,5%)
@componentBackground = lighten(@background,5%)
@menuBackground = lighten(@background,5%)

# selection
@selectionBackground = @accentSelectionBackground
@selectionForeground = contrast(@selectionBackground, @foreground, #fff)
@selectionInactiveBackground = shade(@background,13%)
@selectionInactiveForeground = @foreground

# menu
@menuSelectionBackground = @selectionBackground
@menuHoverBackground = darken(@menuBackground,10%,derived)
@menuCheckBackground = lighten(@menuSelectionBackground,40%,derived noAutoInverse)
@menuAcceleratorForeground = lighten(@foreground,30%)
@menuAcceleratorSelectionForeground = @selectionForeground

# misc
@cellFocusColor = darken(@selectionBackground,20%)
@icon = shade(@background,27%)

# accent colors (blueish)
#   set @accentColor to use single accent color or
#   modify @accentBaseColor to use variations of accent base color
@accentColor = systemColor(accent,null)
@accentBaseColor = #2675BF
@accentBase2Color = lighten(saturate(@accentBaseColor,10%),6%)
#   accent color variations
@accentCheckmarkColor           = if(@accentColor, @accentColor, tint(@accentBase2Color,20%))
@accentFocusColor               = if(@accentColor, @accentColor, lighten(@accentBaseColor,31%))
@accentLinkColor                = if(@accentColor, @accentColor, darken(@accentBaseColor,3%))
@accentSelectionBackground      = if(@accentColor, @accentColor, @accentBaseColor)
@accentSliderColor              = if(@accentColor, @accentColor, @accentBase2Color)
@accentUnderlineColor           = if(@accentColor, @accentColor, tint(@accentBaseColor,10%))
@accentButtonDefaultBorderColor = if(@accentColor, @accentColor, tint(@accentBase2Color,20%))

# for buttons within components (e.g. combobox or spinner)
@buttonArrowColor = tint(@foreground,40%)
@buttonDisabledArrowColor = lighten(@buttonArrowColor,25%)
@buttonHoverArrowColor = lighten(@buttonArrowColor,20%,derived noAutoInverse)
@buttonPressedArrowColor = lighten(@buttonArrowColor,30%,derived noAutoInverse)

# Drop (use lazy colors for IntelliJ platform themes, which usually do not specify these colors)
@dropCellBackground = lighten(List.selectionBackground,10%,lazy)
@dropCellForeground = lazy(List.selectionForeground)
@dropLineColor = lighten(List.selectionBackground,20%,lazy)
@dropLineShortColor = darken(List.selectionBackground,20%,lazy)


#---- system colors ----

activeCaption = #99b4d1
inactiveCaption = #bfcddb
controlHighlight = lighten($controlShadow,12%)
controlLtHighlight = lighten($controlShadow,25%)
controlDkShadow = darken($controlShadow,15%)


#---- Button ----

Button.background = @buttonBackground
Button.focusedBackground = changeLightness($Component.focusColor,95%)
Button.hoverBackground = darken($Button.background,3%,derived)
Button.pressedBackground = darken($Button.background,10%,derived)
Button.selectedBackground = darken($Button.background,20%,derived)
Button.selectedForeground = $Button.foreground
Button.disabledSelectedBackground = darken($Button.background,13%,derived)

Button.borderColor = $Component.borderColor
Button.disabledBorderColor = $Component.disabledBorderColor
Button.focusedBorderColor = $Component.focusedBorderColor
Button.hoverBorderColor = $Button.focusedBorderColor

Button.innerFocusWidth = 0

Button.default.background = $Button.background
Button.default.foreground = $Button.foreground
Button.default.focusedBackground = $Button.focusedBackground
Button.default.hoverBackground = darken($Button.default.background,3%,derived)
Button.default.pressedBackground = darken($Button.default.background,10%,derived)
Button.default.borderColor = @accentButtonDefaultBorderColor
Button.default.hoverBorderColor = $Button.hoverBorderColor
Button.default.focusedBorderColor = $Button.focusedBorderColor
Button.default.focusColor = $Component.focusColor
Button.default.borderWidth = 2

Button.toolbar.hoverBackground = darken($Button.background,12%,derived)
Button.toolbar.pressedBackground = darken($Button.background,15%,derived)
Button.toolbar.selectedBackground = $Button.selectedBackground


#---- CheckBox ----

CheckBox.icon.focusWidth = 1

# enabled
CheckBox.icon.borderColor = shade($Component.borderColor,10%)
CheckBox.icon.background = @buttonBackground
CheckBox.icon.selectedBorderColor = $CheckBox.icon.checkmarkColor
CheckBox.icon.selectedBackground = $CheckBox.icon.background
CheckBox.icon.checkmarkColor = @accentCheckmarkColor

# disabled
CheckBox.icon.disabledBorderColor = tint($CheckBox.icon.borderColor,20%)
CheckBox.icon.disabledBackground = @disabledBackground
CheckBox.icon.disabledCheckmarkColor = lighten(changeSaturation($CheckBox.icon.checkmarkColor,0%),5%)

# focused
CheckBox.icon.focusedBorderColor = shade($Component.focusedBorderColor,10%)
CheckBox.icon.focusedBackground = changeLightness($Component.focusColor,95%)

# hover
CheckBox.icon.hoverBorderColor = $CheckBox.icon.focusedBorderColor
CheckBox.icon.hoverBackground = darken($CheckBox.icon.background,3%,derived)

# pressed
CheckBox.icon.pressedBorderColor = $CheckBox.icon.focusedBorderColor
CheckBox.icon.pressedBackground = darken($CheckBox.icon.background,10%,derived)


# used if CheckBox.icon.style or RadioButton.icon.style = filled
# enabled
CheckBox.icon[filled].selectedBorderColor = shade($CheckBox.icon[filled].selectedBackground,5%)
CheckBox.icon[filled].selectedBackground = @accentCheckmarkColor
CheckBox.icon[filled].checkmarkColor = @buttonBackground
# focused
CheckBox.icon[filled].focusedSelectedBorderColor = tint($CheckBox.icon[filled].selectedBackground,50%)
CheckBox.icon[filled].focusedSelectedBackground = $CheckBox.icon[filled].selectedBackground
CheckBox.icon[filled].focusedCheckmarkColor = $CheckBox.icon.focusedBackground
# hover
CheckBox.icon[filled].hoverSelectedBackground = darken($CheckBox.icon[filled].selectedBackground,5%,derived)
# pressed
CheckBox.icon[filled].pressedSelectedBackground = darken($CheckBox.icon[filled].selectedBackground,10%,derived)


#---- CheckBoxMenuItem ----

CheckBoxMenuItem.icon.checkmarkColor = @accentCheckmarkColor
CheckBoxMenuItem.icon.disabledCheckmarkColor = @buttonDisabledArrowColor


#---- Component ----

Component.borderColor = shade(@background,20%)
Component.disabledBorderColor = tint($Component.borderColor,20%)
Component.focusedBorderColor = shade($Component.focusColor,10%)
Component.focusColor = @accentFocusColor
Component.linkColor = @accentLinkColor
Component.accentColor = if(@accentColor, @accentColor, @accentBaseColor)
Component.grayFilter = 25,-25,100

Component.error.borderColor = lighten(desaturate($Component.error.focusedBorderColor,20%),25%)
Component.error.focusedBorderColor = #e53e4d
Component.warning.borderColor = lighten(saturate($Component.warning.focusedBorderColor,25%),20%)
Component.warning.focusedBorderColor = #e2a53a
Component.success.borderColor = lighten(desaturate($Component.success.focusedBorderColor,20%),25%)
Component.success.focusedBorderColor = #14dc92
Component.custom.borderColor = lighten(desaturate(#f00,20%,derived noAutoInverse),25%,derived noAutoInverse)


#---- Desktop ----

Desktop.background = #E6EBF0


#---- DesktopIcon ----

DesktopIcon.background = darken($Desktop.background,10%,derived)


#---- HelpButton ----

HelpButton.questionMarkColor = @accentCheckmarkColor
HelpButton.disabledQuestionMarkColor = shade(@background,30%)


#---- InternalFrame ----

InternalFrame.activeTitleBackground = #fff
InternalFrame.activeTitleForeground = @foreground
InternalFrame.inactiveTitleBackground = darken($InternalFrame.activeTitleBackground,2%)
InternalFrame.inactiveTitleForeground = @disabledForeground

InternalFrame.activeBorderColor = shade(@background,40%)
InternalFrame.inactiveBorderColor = shade(@background,20%)

InternalFrame.buttonHoverBackground = darken($InternalFrame.activeTitleBackground,10%,derived)
InternalFrame.buttonPressedBackground = darken($InternalFrame.activeTitleBackground,20%,derived)
InternalFrame.closeHoverBackground = lazy(Actions.Red)
InternalFrame.closePressedBackground = darken(Actions.Red,10%,lazy)
InternalFrame.closeHoverForeground = #fff
InternalFrame.closePressedForeground = #fff

InternalFrame.activeDropShadowOpacity = 0.25
InternalFrame.inactiveDropShadowOpacity = 0.5


#---- Menu ----

Menu.icon.arrowColor = @buttonArrowColor
Menu.icon.disabledArrowColor = @buttonDisabledArrowColor


#---- MenuBar ----

MenuBar.borderColor = $Separator.foreground


#---- PasswordField ----

PasswordField.capsLockIconColor = #00000064
PasswordField.revealIconColor = tint(@foreground,40%)


#---- Popup ----

Popup.dropShadowColor = #000
Popup.dropShadowOpacity = 0.15


#---- PopupMenu ----

PopupMenu.borderColor = shade(@background,28%)
PopupMenu.hoverScrollArrowBackground = darken(@background,5%)


#---- ProgressBar ----

ProgressBar.background = darken(@background,13%)
ProgressBar.foreground = @accentSliderColor
ProgressBar.selectionBackground = @foreground
ProgressBar.selectionForeground = contrast($ProgressBar.foreground, @foreground, @componentBackground)


#---- RootPane ----

RootPane.activeBorderColor = darken(@background,50%,derived)
RootPane.inactiveBorderColor = darken(@background,30%,derived)


#---- ScrollBar ----

ScrollBar.track = lighten(@background,1%,derived noAutoInverse)
ScrollBar.thumb = darken($ScrollBar.track,15%,derived noAutoInverse)
ScrollBar.hoverTrackColor = darken($ScrollBar.track,3%,derived noAutoInverse)
ScrollBar.hoverThumbColor = darken($ScrollBar.thumb,10%,derived noAutoInverse)
ScrollBar.pressedThumbColor = darken($ScrollBar.thumb,20%,derived noAutoInverse)
ScrollBar.hoverButtonBackground = darken(@background,5%,derived noAutoInverse)
ScrollBar.pressedButtonBackground = darken(@background,10%,derived noAutoInverse)


#---- Separator ----

Separator.foreground = shade(@background,15%)


#---- Slider ----

Slider.trackValueColor = @accentSliderColor
Slider.trackColor = darken(@background,18%)
Slider.thumbColor = $Slider.trackValueColor
Slider.tickColor = @disabledForeground
Slider.focusedColor = fade(changeLightness($Component.focusColor,75%,derived),50%,derived)
Slider.hoverThumbColor = darken($Slider.thumbColor,5%,derived)
Slider.pressedThumbColor = darken($Slider.thumbColor,8%,derived)
Slider.disabledTrackColor = darken(@background,13%)
Slider.disabledThumbColor = $Slider.disabledTrackColor


#---- SplitPane ----

SplitPaneDivider.draggingColor = $Component.borderColor


#---- TabbedPane ----

TabbedPane.underlineColor = @accentUnderlineColor
TabbedPane.inactiveUnderlineColor = mix(@accentUnderlineColor,$TabbedPane.background,50%)
TabbedPane.disabledUnderlineColor = darken(@background,28%)
TabbedPane.hoverColor = darken($TabbedPane.background,7%,derived)
TabbedPane.focusColor = mix(@selectionBackground,$TabbedPane.background,10%)
TabbedPane.contentAreaColor = $Component.borderColor

TabbedPane.buttonHoverBackground = darken($TabbedPane.background,7%,derived)
TabbedPane.buttonPressedBackground = darken($TabbedPane.background,10%,derived)

TabbedPane.closeBackground = null
TabbedPane.closeForeground = @disabledForeground
TabbedPane.closeHoverBackground = darken($TabbedPane.background,20%,derived)
TabbedPane.closeHoverForeground = @foreground
TabbedPane.closePressedBackground = darken($TabbedPane.background,25%,derived)
TabbedPane.closePressedForeground = $TabbedPane.closeHoverForeground


#---- Table ----

Table.gridColor = darken($Table.background,8%)


#---- TableHeader ----

TableHeader.hoverBackground = darken($TableHeader.background,5%,derived)
TableHeader.pressedBackground = darken($TableHeader.background,10%,derived)
TableHeader.separatorColor = darken($TableHeader.background,10%)
TableHeader.bottomSeparatorColor = $TableHeader.separatorColor


#---- TitlePane ----

TitlePane.embeddedForeground = lighten($TitlePane.foreground,35%)
TitlePane.buttonHoverBackground = darken($TitlePane.background,5%,derived)
TitlePane.buttonPressedBackground = darken($TitlePane.background,3%,derived)

# Linux
[linux]TitlePane.buttonBackground = darken($TitlePane.background,5%,derived)
[linux]TitlePane.buttonHoverBackground = darken($TitlePane.background,10%,derived)
[linux]TitlePane.buttonPressedBackground = darken($TitlePane.background,15%,derived)


#---- ToggleButton ----

ToggleButton.selectedBackground = darken($ToggleButton.background,20%,derived)
ToggleButton.disabledSelectedBackground = darken($ToggleButton.background,13%,derived)

ToggleButton.toolbar.selectedBackground = $ToggleButton.selectedBackground


#---- ToolBar ----

ToolBar.hoverButtonGroupBackground = darken($ToolBar.background,3%,derived)


#---- ToolTip ----

ToolTip.border = 4,6,4,6,shade(@background,40%)
ToolTip.background = lighten(@background,3%)


#---- Tree ----

Tree.hash = darken($Tree.background,10%)



#---- Styles ------------------------------------------------------------------

#---- inTextField ----
# for leading/trailing components in text fields

[style]Button.inTextField = \
	focusable: false; \
	toolbar.margin: 1,1,1,1; \
	toolbar.spacingInsets: 1,1,1,1; \
	toolbar.hoverBackground: darken($TextField.background,4%); \
	toolbar.pressedBackground: darken($TextField.background,8%); \
	toolbar.selectedBackground: darken($TextField.background,12%)
