#
# Copyright 2019 FormDev Software GmbH
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

#
# This file is loaded for all themes.
#
# Documentation:
#  - https://www.formdev.com/flatlaf/properties-files/
#  - https://www.formdev.com/flatlaf/how-to-customize/
#
# NOTE: Avoid copying the whole content of this file to own properties files.
#       This will make upgrading to newer FlatLaf versions complex and error-prone.
#       Instead, copy and modify only those properties that you need to alter.
#

#---- typography / fonts ----

# headings
h00.font = +24
h0.font = +18
h1.font = +12 $semibold.font
h2.font = +6 $semibold.font
h3.font = +3 $semibold.font
h4.font = bold

h1.regular.font = +12
h2.regular.font = +6
h3.regular.font = +3

# text
large.font = +2
medium.font = -1
small.font = -2
mini.font = -3

# default font
#defaultFont = ...

# font weights
#  fallback for unknown platform
light.font = +0
semibold.font = +0
#  Windows
[win]light.font = "Segoe UI Light"
[win]semibold.font = "Segoe UI Semibold"
#  macOS
[mac]light.font = "HelveticaNeue-Thin"
[mac]semibold.font = "HelveticaNeue-Medium"
#  Linux
[linux]light.font = "Lato Light", "Ubuntu Light", "Cantarell Light"
[linux]semibold.font = "Lato Semibold", "Ubuntu Medium", "Montserrat SemiBold"

# monospaced
monospaced.font = Monospaced
[win]monospaced.font = Monospaced
[mac]monospaced.font = Menlo, Monospaced
[linux]monospaced.font = "Liberation Mono", "Ubuntu Mono", Monospaced

# styles
[style].h00 = font: $h00.font
[style].h0 = font: $h0.font
[style].h1 = font: $h1.font
[style].h2 = font: $h2.font
[style].h3 = font: $h3.font
[style].h4 = font: $h4.font
[style].h1.regular = font: $h1.regular.font
[style].h2.regular = font: $h2.regular.font
[style].h3.regular = font: $h3.regular.font
[style].large = font: $large.font
[style].medium = font: $medium.font
[style].small = font: $small.font
[style].mini = font: $mini.font
[style].light = font: $light.font
[style].semibold = font: $semibold.font
[style].monospaced = font: $monospaced.font


#---- UI delegates ----

ButtonUI = com.formdev.flatlaf.ui.FlatButtonUI
CheckBoxUI = com.formdev.flatlaf.ui.FlatCheckBoxUI
CheckBoxMenuItemUI = com.formdev.flatlaf.ui.FlatCheckBoxMenuItemUI
ColorChooserUI = com.formdev.flatlaf.ui.FlatColorChooserUI
ComboBoxUI = com.formdev.flatlaf.ui.FlatComboBoxUI
DesktopIconUI = com.formdev.flatlaf.ui.FlatDesktopIconUI
DesktopPaneUI = com.formdev.flatlaf.ui.FlatDesktopPaneUI
EditorPaneUI = com.formdev.flatlaf.ui.FlatEditorPaneUI
FileChooserUI = com.formdev.flatlaf.ui.FlatFileChooserUI
FormattedTextFieldUI = com.formdev.flatlaf.ui.FlatFormattedTextFieldUI
InternalFrameUI = com.formdev.flatlaf.ui.FlatInternalFrameUI
LabelUI = com.formdev.flatlaf.ui.FlatLabelUI
ListUI = com.formdev.flatlaf.ui.FlatListUI
MenuUI = com.formdev.flatlaf.ui.FlatMenuUI
MenuBarUI = com.formdev.flatlaf.ui.FlatMenuBarUI
MenuItemUI = com.formdev.flatlaf.ui.FlatMenuItemUI
OptionPaneUI = com.formdev.flatlaf.ui.FlatOptionPaneUI
PanelUI = com.formdev.flatlaf.ui.FlatPanelUI
PasswordFieldUI = com.formdev.flatlaf.ui.FlatPasswordFieldUI
PopupMenuUI = com.formdev.flatlaf.ui.FlatPopupMenuUI
PopupMenuSeparatorUI = com.formdev.flatlaf.ui.FlatPopupMenuSeparatorUI
ProgressBarUI = com.formdev.flatlaf.ui.FlatProgressBarUI
RadioButtonUI = com.formdev.flatlaf.ui.FlatRadioButtonUI
RadioButtonMenuItemUI = com.formdev.flatlaf.ui.FlatRadioButtonMenuItemUI
RootPaneUI = com.formdev.flatlaf.ui.FlatRootPaneUI
ScrollBarUI = com.formdev.flatlaf.ui.FlatScrollBarUI
ScrollPaneUI = com.formdev.flatlaf.ui.FlatScrollPaneUI
SeparatorUI = com.formdev.flatlaf.ui.FlatSeparatorUI
SliderUI = com.formdev.flatlaf.ui.FlatSliderUI
SpinnerUI = com.formdev.flatlaf.ui.FlatSpinnerUI
SplitPaneUI = com.formdev.flatlaf.ui.FlatSplitPaneUI
TabbedPaneUI = com.formdev.flatlaf.ui.FlatTabbedPaneUI
TableUI = com.formdev.flatlaf.ui.FlatTableUI
TableHeaderUI = com.formdev.flatlaf.ui.FlatTableHeaderUI
TextAreaUI = com.formdev.flatlaf.ui.FlatTextAreaUI
TextFieldUI = com.formdev.flatlaf.ui.FlatTextFieldUI
TextPaneUI = com.formdev.flatlaf.ui.FlatTextPaneUI
ToggleButtonUI = com.formdev.flatlaf.ui.FlatToggleButtonUI
ToolBarUI = com.formdev.flatlaf.ui.FlatToolBarUI
ToolBarSeparatorUI = com.formdev.flatlaf.ui.FlatToolBarSeparatorUI
ToolTipUI = com.formdev.flatlaf.ui.FlatToolTipUI
TreeUI = com.formdev.flatlaf.ui.FlatTreeUI
ViewportUI = com.formdev.flatlaf.ui.FlatViewportUI


#---- variables ----

@componentMargin = 2,6,2,6
@menuItemMargin = 3,6,3,6


#---- wildcard replacements ----

*.background = @background
*.foreground = @foreground
*.disabledBackground = @disabledBackground
*.disabledForeground = @disabledForeground
*.disabledText = @disabledForeground
*.inactiveBackground = @disabledBackground
*.inactiveForeground = @disabledForeground
*.selectionBackground = @selectionBackground
*.selectionForeground = @selectionForeground
*.caretForeground = @foreground
*.acceleratorForeground = @menuAcceleratorForeground
*.acceleratorSelectionForeground = @menuAcceleratorSelectionForeground


#---- system colors ----

desktop = @componentBackground
activeCaptionText = @foreground
activeCaptionBorder = $activeCaption
inactiveCaptionText = @foreground
inactiveCaptionBorder = $inactiveCaption
window = @background
windowBorder = @foreground
windowText = @foreground
menu = @background
menuText = @foreground
text = @componentBackground
textText = @foreground
textHighlight = @selectionBackground
textHighlightText = @selectionForeground
textInactiveText = @disabledForeground
control = @background
controlText = @foreground
controlShadow = $Component.borderColor
scrollbar = $ScrollBar.track
info = $ToolTip.background
infoText = @foreground


#---- unused colors ----

# Colors that are defined in BasicLookAndFeel but are not used in FlatLaf.
# Keep them for compatibility (if used in 3rd party app) and give them useful values.

*.shadow = $controlShadow
*.darkShadow = $controlDkShadow
*.light = $controlHighlight
*.highlight = $controlLtHighlight

ComboBox.buttonShadow = $controlShadow
ComboBox.buttonDarkShadow = $controlDkShadow
ComboBox.buttonHighlight = $controlLtHighlight

InternalFrame.borderColor = $control
InternalFrame.borderShadow = $controlShadow
InternalFrame.borderDarkShadow = $controlDkShadow
InternalFrame.borderHighlight = $controlLtHighlight
InternalFrame.borderLight = $controlHighlight

Label.disabledShadow = $controlShadow

ScrollBar.trackHighlight = $controlDkShadow
ScrollBar.thumbHighlight = $controlLtHighlight
ScrollBar.thumbDarkShadow = $controlDkShadow
ScrollBar.thumbShadow = $controlShadow

Slider.focus = $controlDkShadow

TabbedPane.focus = $controlText


#---- Button ----

Button.border = com.formdev.flatlaf.ui.FlatButtonBorder
Button.arc = 6
Button.minimumWidth = 72
Button.margin = 2,14,2,14
Button.iconTextGap = 4
Button.rollover = true
Button.defaultButtonFollowsFocus = false

Button.borderWidth = 1
Button.default.borderWidth = 1

# for buttons in toolbars
Button.toolbar.margin = 3,3,3,3
Button.toolbar.spacingInsets = 1,2,1,2


#---- Caret ----

Caret.width = {scaledInteger}1


#---- CheckBox ----

CheckBox.border = com.formdev.flatlaf.ui.FlatMarginBorder
CheckBox.icon = com.formdev.flatlaf.icons.FlatCheckBoxIcon
CheckBox.arc = 4
CheckBox.margin = 2,2,2,2
CheckBox.iconTextGap = 4
CheckBox.rollover = true


#---- CheckBoxMenuItem ----

CheckBoxMenuItem.border = com.formdev.flatlaf.ui.FlatMenuItemBorder
CheckBoxMenuItem.checkIcon = com.formdev.flatlaf.icons.FlatCheckBoxMenuItemIcon
CheckBoxMenuItem.arrowIcon = com.formdev.flatlaf.icons.FlatMenuItemArrowIcon
CheckBoxMenuItem.margin = @menuItemMargin
CheckBoxMenuItem.opaque = false
CheckBoxMenuItem.borderPainted = true
CheckBoxMenuItem.background = @menuBackground


#---- ColorChooser ----

ColorChooser.swatchesSwatchSize = {scaledDimension}16,16
ColorChooser.swatchesRecentSwatchSize = {scaledDimension}16,16
ColorChooser.swatchesDefaultRecentColor = $control


#---- ComboBox ----

ComboBox.border = com.formdev.flatlaf.ui.FlatRoundBorder
ComboBox.padding = @componentMargin
ComboBox.minimumWidth = 72
ComboBox.editorColumns = 0
ComboBox.maximumRowCount = 15
[mac]ComboBox.showPopupOnNavigation = true
# allowed values: auto, button or none
ComboBox.buttonStyle = auto
ComboBox.background = @componentBackground
ComboBox.buttonBackground = $ComboBox.background
ComboBox.buttonEditableBackground = darken($ComboBox.background,2%)
ComboBox.buttonSeparatorColor = $Component.borderColor
ComboBox.buttonDisabledSeparatorColor = $Component.disabledBorderColor
ComboBox.buttonArrowColor = @buttonArrowColor
ComboBox.buttonDisabledArrowColor = @buttonDisabledArrowColor
ComboBox.buttonHoverArrowColor = @buttonHoverArrowColor
ComboBox.buttonPressedArrowColor = @buttonPressedArrowColor

ComboBox.popupInsets = 0,0,0,0
ComboBox.selectionInsets = 0,0,0,0
ComboBox.selectionArc = 0
ComboBox.borderCornerRadius = $Popup.borderCornerRadius
[mac]ComboBox.roundedBorderWidth = $Popup.roundedBorderWidth


#---- Component ----

Component.focusWidth = 0
Component.innerFocusWidth = 0.5
Component.innerOutlineWidth = 1
Component.borderWidth = 1
Component.arc = 5
Component.minimumWidth = 64
# allowed values: chevron or triangle
Component.arrowType = chevron
Component.hideMnemonics = true


#---- DesktopIcon ----

DesktopIcon.border = 4,4,4,4
DesktopIcon.iconSize = 64,64
DesktopIcon.closeSize = 20,20
DesktopIcon.closeIcon = com.formdev.flatlaf.icons.FlatInternalFrameCloseIcon


#---- EditorPane ----

EditorPane.border = com.formdev.flatlaf.ui.FlatMarginBorder
EditorPane.margin = @componentMargin
EditorPane.background = @componentBackground


#---- FileChooser ----

FileChooser.newFolderIcon = com.formdev.flatlaf.icons.FlatFileChooserNewFolderIcon
FileChooser.upFolderIcon = com.formdev.flatlaf.icons.FlatFileChooserUpFolderIcon
FileChooser.homeFolderIcon = com.formdev.flatlaf.icons.FlatFileChooserHomeFolderIcon
FileChooser.detailsViewIcon = com.formdev.flatlaf.icons.FlatFileChooserDetailsViewIcon
FileChooser.listViewIcon = com.formdev.flatlaf.icons.FlatFileChooserListViewIcon
FileChooser.usesSingleFilePane = true
[win]FileChooser.useSystemExtensionHiding = true


#---- FileView ----

FileView.directoryIcon = com.formdev.flatlaf.icons.FlatFileViewDirectoryIcon
FileView.fileIcon = com.formdev.flatlaf.icons.FlatFileViewFileIcon
FileView.computerIcon = com.formdev.flatlaf.icons.FlatFileViewComputerIcon
FileView.hardDriveIcon = com.formdev.flatlaf.icons.FlatFileViewHardDriveIcon
FileView.floppyDriveIcon = com.formdev.flatlaf.icons.FlatFileViewFloppyDriveIcon
FileView.fullRowSelection = true


#---- FormattedTextField ----

FormattedTextField.border = com.formdev.flatlaf.ui.FlatTextBorder
FormattedTextField.margin = @componentMargin
FormattedTextField.background = @componentBackground
FormattedTextField.placeholderForeground = @disabledForeground
FormattedTextField.iconTextGap = 4


#---- HelpButton ----

HelpButton.icon = com.formdev.flatlaf.icons.FlatHelpButtonIcon
HelpButton.borderColor = $Button.borderColor
HelpButton.disabledBorderColor = $Button.disabledBorderColor
HelpButton.focusedBorderColor = $Button.focusedBorderColor
HelpButton.hoverBorderColor = $?Button.hoverBorderColor
HelpButton.background = $Button.background
HelpButton.disabledBackground = $Button.disabledBackground
HelpButton.focusedBackground = $?Button.focusedBackground
HelpButton.hoverBackground = $?Button.hoverBackground
HelpButton.pressedBackground = $?Button.pressedBackground

HelpButton.borderWidth = $?Button.borderWidth
HelpButton.innerFocusWidth = $?Button.innerFocusWidth


#---- InternalFrame ----

InternalFrame.border = com.formdev.flatlaf.ui.FlatInternalFrameUI$FlatInternalFrameBorder
InternalFrame.borderLineWidth = 1
InternalFrame.borderMargins = 6,6,6,6
InternalFrame.buttonSize = 24,24
InternalFrame.closeIcon = com.formdev.flatlaf.icons.FlatInternalFrameCloseIcon
InternalFrame.iconifyIcon = com.formdev.flatlaf.icons.FlatInternalFrameIconifyIcon
InternalFrame.maximizeIcon = com.formdev.flatlaf.icons.FlatInternalFrameMaximizeIcon
InternalFrame.minimizeIcon = com.formdev.flatlaf.icons.FlatInternalFrameRestoreIcon
InternalFrame.windowBindings = null

# drop shadow
InternalFrame.dropShadowPainted = true
InternalFrame.activeDropShadowColor = null
InternalFrame.activeDropShadowInsets = 5,5,6,6
InternalFrame.inactiveDropShadowColor = null
InternalFrame.inactiveDropShadowInsets = 3,3,4,4


#---- InternalFrameTitlePane ----

InternalFrameTitlePane.border = 0,8,0,0


#---- List ----

List.border = 0,0,0,0
List.cellMargins = 1,6,1,6
List.selectionInsets = 0,0,0,0
List.selectionArc = 0
List.cellFocusColor = @cellFocusColor
List.cellNoFocusBorder = com.formdev.flatlaf.ui.FlatListCellBorder$Default
List.focusCellHighlightBorder = com.formdev.flatlaf.ui.FlatListCellBorder$Focused
List.focusSelectedCellHighlightBorder = com.formdev.flatlaf.ui.FlatListCellBorder$Selected
List.background = @componentBackground
List.selectionInactiveBackground = @selectionInactiveBackground
List.selectionInactiveForeground = @selectionInactiveForeground
List.dropCellBackground = @dropCellBackground
List.dropCellForeground = @dropCellForeground
List.dropLineColor = @dropLineColor
List.showCellFocusIndicator = false


#---- Menu ----

Menu.border = com.formdev.flatlaf.ui.FlatMenuItemBorder
Menu.arrowIcon = com.formdev.flatlaf.icons.FlatMenuArrowIcon
Menu.checkIcon = null
Menu.margin = @menuItemMargin
Menu.submenuPopupOffsetX = {scaledInteger}-4
Menu.submenuPopupOffsetY = {scaledInteger}-4
Menu.opaque = false
Menu.borderPainted = true
Menu.background = @menuBackground


#---- MenuBar ----

MenuBar.border = com.formdev.flatlaf.ui.FlatMenuBarBorder
MenuBar.background = @menuBackground
MenuBar.hoverBackground = @menuHoverBackground
MenuBar.itemMargins = 3,8,3,8
MenuBar.selectionInsets = $MenuItem.selectionInsets
MenuBar.selectionEmbeddedInsets = $MenuItem.selectionInsets
MenuBar.selectionArc = $MenuItem.selectionArc


#---- MenuItem ----

MenuItem.border = com.formdev.flatlaf.ui.FlatMenuItemBorder
MenuItem.arrowIcon = com.formdev.flatlaf.icons.FlatMenuItemArrowIcon
MenuItem.checkIcon = null
MenuItem.margin = @menuItemMargin
MenuItem.opaque = false
MenuItem.borderPainted = true
MenuItem.verticallyAlignText = true
MenuItem.background = @menuBackground
MenuItem.checkBackground = @menuCheckBackground
MenuItem.checkMargins = 2,2,2,2
MenuItem.minimumWidth = 72
MenuItem.minimumIconSize = 16,16
MenuItem.iconTextGap = 6
MenuItem.textAcceleratorGap = 24
MenuItem.textNoAcceleratorGap = 6
MenuItem.acceleratorArrowGap = 2
MenuItem.acceleratorDelimiter = "+"
[mac]MenuItem.acceleratorDelimiter = ""
MenuItem.selectionInsets = 0,0,0,0
MenuItem.selectionArc = 0

# for MenuItem.selectionType = underline
MenuItem.underlineSelectionBackground = @menuHoverBackground
MenuItem.underlineSelectionCheckBackground = @menuCheckBackground
MenuItem.underlineSelectionColor = @accentUnderlineColor
MenuItem.underlineSelectionHeight = 3


#---- OptionPane ----

OptionPane.border = 12,12,12,12
OptionPane.messageAreaBorder = 0,0,0,0
OptionPane.buttonAreaBorder = 12,0,0,0
OptionPane.messageForeground = null

OptionPane.showIcon = false
OptionPane.maxCharactersPerLine = 80
OptionPane.iconMessageGap = 16
OptionPane.messagePadding = 3
OptionPane.buttonPadding = 8
OptionPane.buttonMinimumWidth = {scaledInteger}72
OptionPane.sameSizeButtons = true
OptionPane.setButtonMargin = false
OptionPane.buttonOrientation = 4
[mac]OptionPane.isYesLast = true

OptionPane.errorIcon = com.formdev.flatlaf.icons.FlatOptionPaneErrorIcon
OptionPane.informationIcon = com.formdev.flatlaf.icons.FlatOptionPaneInformationIcon
OptionPane.questionIcon = com.formdev.flatlaf.icons.FlatOptionPaneQuestionIcon
OptionPane.warningIcon = com.formdev.flatlaf.icons.FlatOptionPaneWarningIcon


#---- PasswordField ----

PasswordField.border = com.formdev.flatlaf.ui.FlatTextBorder
PasswordField.margin = @componentMargin
PasswordField.background = @componentBackground
PasswordField.placeholderForeground = @disabledForeground
PasswordField.iconTextGap = 4
PasswordField.echoChar = \u2022
PasswordField.showCapsLock = true
PasswordField.showRevealButton = false
PasswordField.capsLockIcon = com.formdev.flatlaf.icons.FlatCapsLockIcon
PasswordField.revealIcon = com.formdev.flatlaf.icons.FlatRevealIcon


#---- Popup ----

Popup.borderCornerRadius = 4
[mac]Popup.roundedBorderWidth = 0
Popup.dropShadowPainted = true
Popup.dropShadowInsets = -4,-4,4,4


#---- PopupMenu ----

PopupMenu.border = com.formdev.flatlaf.ui.FlatPopupMenuBorder
PopupMenu.borderInsets = 4,1,4,1
PopupMenu.borderCornerRadius = $Popup.borderCornerRadius
[mac]PopupMenu.roundedBorderWidth = $Popup.roundedBorderWidth
PopupMenu.background = @menuBackground
PopupMenu.scrollArrowColor = @buttonArrowColor


#---- PopupMenuSeparator ----

PopupMenuSeparator.height = 9
PopupMenuSeparator.stripeWidth = 1
PopupMenuSeparator.stripeIndent = 4


#---- ProgressBar ----

ProgressBar.border = com.formdev.flatlaf.ui.FlatEmptyBorder
ProgressBar.arc = 4
ProgressBar.horizontalSize = 146,4
ProgressBar.verticalSize = 4,146
ProgressBar.cycleTime = 4000
ProgressBar.repaintInterval = 15
ProgressBar.font = -2


#---- RadioButton ----

RadioButton.border = com.formdev.flatlaf.ui.FlatMarginBorder
RadioButton.icon = com.formdev.flatlaf.icons.FlatRadioButtonIcon
RadioButton.icon.centerDiameter = 8
RadioButton.icon[filled].centerDiameter = 5
RadioButton.margin = 2,2,2,2
RadioButton.iconTextGap = 4
RadioButton.rollover = true


#---- RadioButtonMenuItem ----

RadioButtonMenuItem.border = com.formdev.flatlaf.ui.FlatMenuItemBorder
RadioButtonMenuItem.checkIcon = com.formdev.flatlaf.icons.FlatRadioButtonMenuItemIcon
RadioButtonMenuItem.arrowIcon = com.formdev.flatlaf.icons.FlatMenuItemArrowIcon
RadioButtonMenuItem.margin = @menuItemMargin
RadioButtonMenuItem.opaque = false
RadioButtonMenuItem.borderPainted = true
RadioButtonMenuItem.background = @menuBackground


#---- RootPane ----

RootPane.border = com.formdev.flatlaf.ui.FlatRootPaneUI$FlatWindowBorder
RootPane.borderDragThickness = 6
RootPane.cornerDragWidth = 32
RootPane.honorFrameMinimumSizeOnResize = false
RootPane.honorDialogMinimumSizeOnResize = true


#---- ScrollBar ----

ScrollBar.width = 10
ScrollBar.minimumButtonSize = 12,12
ScrollBar.minimumThumbSize = 18,18
ScrollBar.maximumThumbSize = 100000,100000
ScrollBar.trackInsets = 0,0,0,0
ScrollBar.thumbInsets = 2,2,2,2
ScrollBar.trackArc = 0
ScrollBar.thumbArc = 999
ScrollBar.hoverThumbWithTrack = false
ScrollBar.pressedThumbWithTrack = false
ScrollBar.showButtons = false
ScrollBar.squareButtons = false
ScrollBar.buttonArrowColor = @buttonArrowColor
ScrollBar.buttonDisabledArrowColor = @buttonDisabledArrowColor
ScrollBar.allowsAbsolutePositioning = true

[mac]ScrollBar.hoverThumbWithTrack = true


#---- ScrollPane ----

ScrollPane.border = com.formdev.flatlaf.ui.FlatScrollPaneBorder
ScrollPane.background = $ScrollBar.track
ScrollPane.fillUpperCorner = true
ScrollPane.smoothScrolling = true
ScrollPane.arc = 0
#ScrollPane.List.arc = -1
#ScrollPane.Table.arc = -1
#ScrollPane.TextComponent.arc = -1
#ScrollPane.Tree.arc = -1


#---- SearchField ----

SearchField.searchIconColor = fade(Actions.GreyInline,90%,lazy)
SearchField.searchIconHoverColor = fade(Actions.GreyInline,70%,lazy)
SearchField.searchIconPressedColor = fade(Actions.GreyInline,50%,lazy)

SearchField.clearIconColor = fade(Actions.GreyInline,50%,lazy)
SearchField.clearIconHoverColor = $SearchField.clearIconColor
SearchField.clearIconPressedColor = fade(Actions.GreyInline,80%,lazy)


#---- Separator ----

Separator.height = 3
Separator.stripeWidth = 1
Separator.stripeIndent = 1


#---- Slider ----

Slider.focusInsets = 0,0,0,0
Slider.trackWidth = 2
Slider.thumbSize = 12,12
Slider.focusWidth = 4


#---- Spinner ----

Spinner.border = com.formdev.flatlaf.ui.FlatRoundBorder
Spinner.background = @componentBackground
Spinner.buttonBackground = darken($Spinner.background,2%)
Spinner.buttonSeparatorColor = $Component.borderColor
Spinner.buttonDisabledSeparatorColor = $Component.disabledBorderColor
Spinner.buttonArrowColor = @buttonArrowColor
Spinner.buttonDisabledArrowColor = @buttonDisabledArrowColor
Spinner.buttonHoverArrowColor = @buttonHoverArrowColor
Spinner.buttonPressedArrowColor = @buttonPressedArrowColor
Spinner.padding = @componentMargin
Spinner.editorBorderPainted = false
# allowed values: button or none
Spinner.buttonStyle = button


#---- SplitPane ----

SplitPane.dividerSize = 5
SplitPane.continuousLayout = true
SplitPane.border = null
SplitPane.centerOneTouchButtons = true
SplitPane.oneTouchButtonSize = {scaledInteger}6
SplitPane.oneTouchButtonOffset = {scaledInteger}2

SplitPaneDivider.border = null
SplitPaneDivider.oneTouchArrowColor = @buttonArrowColor
SplitPaneDivider.oneTouchHoverArrowColor = @buttonHoverArrowColor
SplitPaneDivider.oneTouchPressedArrowColor = @buttonPressedArrowColor
# allowed values: grip or plain
SplitPaneDivider.style = grip
SplitPaneDivider.gripColor = @icon
SplitPaneDivider.gripDotCount = 3
SplitPaneDivider.gripDotSize = 3
SplitPaneDivider.gripGap = 2


#---- TabbedPane ----

TabbedPane.tabHeight = 32
TabbedPane.tabSelectionHeight = 3
TabbedPane.cardTabSelectionHeight = 3
TabbedPane.tabArc = 0
TabbedPane.tabSelectionArc = 0
TabbedPane.cardTabArc = 12
TabbedPane.selectedInsets = 0,0,0,0
TabbedPane.tabSelectionInsets = 0,0,0,0
TabbedPane.contentSeparatorHeight = 1
TabbedPane.showTabSeparators = false
TabbedPane.tabSeparatorsFullHeight = false
TabbedPane.hasFullBorder = false
TabbedPane.tabInsets = 4,12,4,12
TabbedPane.tabAreaInsets = 0,0,0,0
TabbedPane.selectedTabPadInsets = 0,0,0,0
TabbedPane.tabRunOverlay = 0
TabbedPane.tabsOverlapBorder = false
TabbedPane.disabledForeground = @disabledForeground
TabbedPane.shadow = @background
TabbedPane.contentBorderInsets = null
# allowed values: moreTabsButton or arrowButtons
TabbedPane.hiddenTabsNavigation = moreTabsButton
# allowed values: leading, trailing, center or fill
TabbedPane.tabAreaAlignment = leading
# allowed values: leading, trailing or center
TabbedPane.tabAlignment = center
# allowed values: preferred, equal or compact
TabbedPane.tabWidthMode = preferred
# allowed values: none, auto, left or right
TabbedPane.tabRotation = none

# allowed values: underlined or card
TabbedPane.tabType = underlined

# allowed values: chevron or triangle
TabbedPane.arrowType = chevron
TabbedPane.buttonInsets = 2,1,2,1
TabbedPane.buttonArc = $Button.arc

# allowed values: wrap or scroll
#TabbedPane.tabLayoutPolicy = scroll
# allowed values: never or asNeeded 
TabbedPane.tabsPopupPolicy = asNeeded
# allowed values: never, asNeeded or asNeededSingle
TabbedPane.scrollButtonsPolicy = asNeededSingle
# allowed values: both or trailing
TabbedPane.scrollButtonsPlacement = both

TabbedPane.closeIcon = com.formdev.flatlaf.icons.FlatTabbedPaneCloseIcon
TabbedPane.closeSize = 16,16
TabbedPane.closeArc = 4
TabbedPane.closeCrossPlainSize = 7.5
TabbedPane.closeCrossFilledSize = $TabbedPane.closeCrossPlainSize
TabbedPane.closeCrossLineWidth = 1


#---- Table ----

Table.rowHeight = 20
Table.showHorizontalLines = false
Table.showVerticalLines = false
Table.showTrailingVerticalLine = false
Table.paintOutsideAlternateRows = false
Table.editorSelectAllOnStartEditing = true
Table.consistentHomeEndKeyBehavior = true
Table.intercellSpacing = 0,0
Table.scrollPaneBorder = com.formdev.flatlaf.ui.FlatScrollPaneBorder
Table.ascendingSortIcon = com.formdev.flatlaf.icons.FlatAscendingSortIcon
Table.descendingSortIcon = com.formdev.flatlaf.icons.FlatDescendingSortIcon
Table.sortIconColor = @icon
Table.cellMargins = 2,3,2,3
Table.cellFocusColor = @cellFocusColor
Table.cellNoFocusBorder = com.formdev.flatlaf.ui.FlatTableCellBorder$Default
Table.focusCellHighlightBorder = com.formdev.flatlaf.ui.FlatTableCellBorder$Focused
Table.focusSelectedCellHighlightBorder = com.formdev.flatlaf.ui.FlatTableCellBorder$Selected
Table.focusCellBackground = $Table.background
Table.focusCellForeground = $Table.foreground
Table.background = @componentBackground
Table.selectionInactiveBackground = @selectionInactiveBackground
Table.selectionInactiveForeground = @selectionInactiveForeground
Table.dropCellBackground = @dropCellBackground
Table.dropCellForeground = @dropCellForeground
Table.dropLineColor = @dropLineColor
Table.dropLineShortColor = @dropLineShortColor


#---- TableHeader ----

TableHeader.height = 25
TableHeader.cellBorder = com.formdev.flatlaf.ui.FlatTableHeaderBorder
TableHeader.cellMargins = 2,3,2,3
TableHeader.focusCellBackground = $TableHeader.background
TableHeader.background = @componentBackground
TableHeader.showTrailingVerticalLine = false


#---- TextArea ----

TextArea.border = com.formdev.flatlaf.ui.FlatMarginBorder
TextArea.margin = @componentMargin
TextArea.background = @componentBackground


#---- TextComponent ----

# allowed values: never, once or always
TextComponent.selectAllOnFocusPolicy = once
TextComponent.selectAllOnMouseClick = false
TextComponent.arc = 0


#---- TextField ----

TextField.border = com.formdev.flatlaf.ui.FlatTextBorder
TextField.margin = @componentMargin
TextField.background = @componentBackground
TextField.placeholderForeground = @disabledForeground
TextField.iconTextGap = 4


#---- TextPane ----

TextPane.border = com.formdev.flatlaf.ui.FlatMarginBorder
TextPane.margin = @componentMargin
TextPane.background = @componentBackground


#---- TitledBorder ----

TitledBorder.titleColor = @foreground
TitledBorder.border = 1,1,1,1,$Separator.foreground


#---- TitlePane ----

TitlePane.useWindowDecorations = true
TitlePane.menuBarEmbedded = true
TitlePane.unifiedBackground = true
TitlePane.showIcon = true
TitlePane.showIconInDialogs = true
TitlePane.noIconLeftGap = 8
TitlePane.iconSize = 16,16
TitlePane.iconMargins = 3,8,3,8
TitlePane.titleMargins = 3,0,3,0
TitlePane.titleMinimumWidth = 60
TitlePane.buttonSize = 44,30
TitlePane.buttonInsets = 0,0,0,0
TitlePane.buttonArc = 0
TitlePane.buttonMinimumWidth = 30
TitlePane.buttonMaximizedHeight = 22
TitlePane.buttonSymbolHeight = 10
TitlePane.buttonsGap = 0
TitlePane.buttonsMargins = 0,0,0,0
TitlePane.buttonsFillVertically = true
TitlePane.centerTitle = false
TitlePane.centerTitleIfMenuBarEmbedded = true
TitlePane.showIconBesideTitle = false
TitlePane.menuBarTitleGap = 40
TitlePane.menuBarTitleMinimumGap = 12
TitlePane.closeIcon = com.formdev.flatlaf.icons.FlatWindowCloseIcon
TitlePane.iconifyIcon = com.formdev.flatlaf.icons.FlatWindowIconifyIcon
TitlePane.maximizeIcon = com.formdev.flatlaf.icons.FlatWindowMaximizeIcon
TitlePane.restoreIcon = com.formdev.flatlaf.icons.FlatWindowRestoreIcon

TitlePane.background = $MenuBar.background
TitlePane.inactiveBackground = $TitlePane.background
TitlePane.foreground = @foreground
TitlePane.inactiveForeground = @disabledForeground

TitlePane.closeHoverBackground = #c42b1c
TitlePane.closePressedBackground = fade($TitlePane.closeHoverBackground,90%)
TitlePane.closeHoverForeground = #fff
TitlePane.closePressedForeground = #fff

# window style "small"
TitlePane.small.font = -1
TitlePane.small.showIcon = false
TitlePane.small.buttonSize = 30,20
TitlePane.small.buttonSymbolHeight = 8
TitlePane.small.closeIcon = com.formdev.flatlaf.icons.FlatWindowCloseIcon, small
TitlePane.small.iconifyIcon = com.formdev.flatlaf.icons.FlatWindowIconifyIcon, small
TitlePane.small.maximizeIcon = com.formdev.flatlaf.icons.FlatWindowMaximizeIcon, small
TitlePane.small.restoreIcon = com.formdev.flatlaf.icons.FlatWindowRestoreIcon, small

# Linux
[linux]TitlePane.buttonSize = 26,26
[linux]TitlePane.buttonInsets = 2,2,2,2
[linux]TitlePane.buttonArc = 999
[linux]TitlePane.buttonMaximizedHeight = -1
[linux]TitlePane.buttonSymbolHeight = 8
[linux]TitlePane.buttonsGap = 8
[linux]TitlePane.buttonsMargins = 4,4,4,4
[linux]TitlePane.buttonsFillVertically = false
[linux]TitlePane.small.buttonSize = 20,20
[linux]TitlePane.small.buttonInsets = 1,1,1,1
[linux]TitlePane.small.buttonSymbolHeight = 6
[linux]TitlePane.small.buttonsGap = 4
[linux]TitlePane.small.buttonsMargins = 2,2,2,2
[linux]TitlePane.closeBackground = $?TitlePane.buttonBackground
[linux]TitlePane.closeInactiveBackground = $?TitlePane.buttonInactiveBackground
[linux]TitlePane.closeHoverBackground = $?TitlePane.buttonHoverBackground
[linux]TitlePane.closePressedBackground = $?TitlePane.buttonPressedBackground
[linux]TitlePane.closeHoverForeground = $?TitlePane.foreground
[linux]TitlePane.closePressedForeground = $?TitlePane.foreground


#---- ToggleButton ----

ToggleButton.border = $Button.border
ToggleButton.margin = $Button.margin
ToggleButton.iconTextGap = $Button.iconTextGap
ToggleButton.rollover = $Button.rollover

ToggleButton.background = $Button.background
ToggleButton.pressedBackground = $Button.pressedBackground
ToggleButton.selectedForeground = $ToggleButton.foreground

ToggleButton.toolbar.hoverBackground = $Button.toolbar.hoverBackground
ToggleButton.toolbar.pressedBackground = $Button.toolbar.pressedBackground

# button type "tab"
ToggleButton.tab.underlineHeight = 2
ToggleButton.tab.underlineColor = $TabbedPane.underlineColor
ToggleButton.tab.disabledUnderlineColor = $TabbedPane.disabledUnderlineColor
ToggleButton.tab.selectedBackground = $?TabbedPane.selectedBackground
ToggleButton.tab.selectedForeground = $?TabbedPane.selectedForeground
ToggleButton.tab.hoverBackground = $TabbedPane.hoverColor
ToggleButton.tab.focusBackground = $TabbedPane.focusColor


#---- ToolBar ----

ToolBar.border = com.formdev.flatlaf.ui.FlatToolBarBorder
ToolBar.borderMargins = 2,2,2,2
ToolBar.isRollover = true
ToolBar.focusableButtons = false
ToolBar.arrowKeysOnlyNavigation = true
ToolBar.hoverButtonGroupArc = 8
ToolBar.floatable = false
ToolBar.gripColor = @icon
ToolBar.dockingBackground = darken($ToolBar.background,5%)
ToolBar.dockingForeground = $Component.borderColor
ToolBar.floatingBackground = $ToolBar.background
ToolBar.floatingForeground = $Component.borderColor

ToolBar.separatorSize = null
ToolBar.separatorWidth = 7
ToolBar.separatorColor = $Separator.foreground

# not used in FlatLaf; intended for custom components in toolbar
# https://github.com/JFormDesigner/FlatLaf/issues/56#issuecomment-586297814
ToolBar.spacingBorder = $Button.toolbar.spacingInsets


#---- ToolTipManager ----

ToolTipManager.enableToolTipMode = activeApplication


#---- ToolTip ----

ToolTip.borderCornerRadius = $Popup.borderCornerRadius
[mac]ToolTip.roundedBorderWidth = $Popup.roundedBorderWidth


#---- Tree ----

Tree.border = 1,1,1,1
Tree.editorBorder = 1,1,1,1,@cellFocusColor
Tree.background = @componentBackground
Tree.selectionInactiveBackground = @selectionInactiveBackground
Tree.selectionInactiveForeground = @selectionInactiveForeground
Tree.textBackground = $Tree.background
Tree.textForeground = $Tree.foreground
Tree.selectionBorderColor = @cellFocusColor
Tree.dropCellBackground = @dropCellBackground
Tree.dropCellForeground = @dropCellForeground
Tree.dropLineColor = @dropLineColor
Tree.rendererFillBackground = false
Tree.rendererMargins = 1,2,1,2
Tree.selectionInsets = 0,0,0,0
Tree.selectionArc = 0
Tree.wideSelection = true
Tree.wideCellRenderer = false
Tree.repaintWholeRow = true
Tree.paintLines = false
Tree.showCellFocusIndicator = false
Tree.showDefaultIcons = false
Tree.leftChildIndent = 7
Tree.rightChildIndent = 11
Tree.rowHeight = 0

Tree.expandedIcon = com.formdev.flatlaf.icons.FlatTreeExpandedIcon
Tree.collapsedIcon = com.formdev.flatlaf.icons.FlatTreeCollapsedIcon
Tree.leafIcon = com.formdev.flatlaf.icons.FlatTreeLeafIcon
Tree.closedIcon = com.formdev.flatlaf.icons.FlatTreeClosedIcon
Tree.openIcon = com.formdev.flatlaf.icons.FlatTreeOpenIcon

Tree.icon.expandedColor = @icon
Tree.icon.collapsedColor = @icon
Tree.icon.leafColor = @icon
Tree.icon.closedColor = @icon
Tree.icon.openColor = @icon


#---- Styles ------------------------------------------------------------------

#---- inTextField ----
# for leading/trailing components in text fields

[style]ToggleButton.inTextField = $[style]Button.inTextField

[style]ToolBar.inTextField = \
	floatable: false; \
	opaque: false; \
	borderMargins: 0,0,0,0

[style]ToolBarSeparator.inTextField = \
	separatorWidth: 3


#---- clearButton ----
# for clear/cancel button in text fields

[style]Button.clearButton = \
	icon: com.formdev.flatlaf.icons.FlatClearIcon; \
	focusable: false; \
	toolbar.margin: 1,1,1,1; \
	toolbar.spacingInsets: 1,1,1,1; \
	toolbar.hoverBackground: null; \
	toolbar.pressedBackground: null
